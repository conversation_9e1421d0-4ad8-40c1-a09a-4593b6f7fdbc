<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Flickering Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }
        .modal-content {
            background: white;
            margin: 50px auto;
            padding: 20px;
            width: 80%;
            max-width: 500px;
            border-radius: 8px;
        }
        .close {
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        .event-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Flickering Debug Test</h1>
    
    <div class="test-container">
        <h3>Event Listener Test</h3>
        <p>This test helps identify if multiple event listeners are being added.</p>
        
        <button class="test-button" id="testBtn1">Test Button 1</button>
        <button class="test-button" id="testBtn2">Test Button 2</button>
        <button class="test-button" id="addListenersBtn">Add More Listeners (Bad)</button>
        <button class="test-button" id="clearLogBtn">Clear Log</button>
        
        <h4>Event Log:</h4>
        <div class="event-log" id="eventLog"></div>
    </div>
    
    <div class="test-container">
        <h3>Modal Test</h3>
        <p>Test modal opening/closing behavior.</p>
        
        <button class="test-button" id="openModalBtn">Open Modal</button>
        
        <div id="testModal" class="modal">
            <div class="modal-content">
                <span class="close" id="closeModal">&times;</span>
                <h4>Test Modal</h4>
                <p>This is a test modal to check for flickering issues.</p>
                <button class="test-button" id="modalActionBtn">Modal Action</button>
            </div>
        </div>
    </div>
    
    <div class="test-container">
        <h3>Dynamic Content Test</h3>
        <p>Test adding/removing content dynamically.</p>
        
        <button class="test-button" id="addContentBtn">Add Content</button>
        <button class="test-button" id="removeContentBtn">Remove Content</button>
        
        <div id="dynamicContent"></div>
    </div>

    <script>
        // Event logging function
        function logEvent(message) {
            const log = document.getElementById('eventLog');
            const timestamp = new Date().toLocaleTimeString();
            log.innerHTML += `[${timestamp}] ${message}\n`;
            log.scrollTop = log.scrollHeight;
        }

        // Test 1: Proper event delegation (good)
        let eventListenerAdded = false;
        
        function setupEventListeners() {
            if (!eventListenerAdded) {
                eventListenerAdded = true;
                logEvent('Setting up event delegation (GOOD)');
                
                document.addEventListener('click', function(e) {
                    if (e.target.id === 'testBtn1') {
                        logEvent('Test Button 1 clicked via delegation');
                    } else if (e.target.id === 'testBtn2') {
                        logEvent('Test Button 2 clicked via delegation');
                    }
                });
            } else {
                logEvent('Event listeners already set up - skipping (GOOD)');
            }
        }

        // Test 2: Bad practice - adding multiple listeners
        function addBadListeners() {
            logEvent('Adding direct listeners (BAD - will cause multiple events)');
            
            document.getElementById('testBtn1').addEventListener('click', function() {
                logEvent('Test Button 1 clicked via DIRECT listener');
            });
            
            document.getElementById('testBtn2').addEventListener('click', function() {
                logEvent('Test Button 2 clicked via DIRECT listener');
            });
        }

        // Test 3: Modal functionality
        function setupModal() {
            const modal = document.getElementById('testModal');
            const openBtn = document.getElementById('openModalBtn');
            const closeBtn = document.getElementById('closeModal');
            const actionBtn = document.getElementById('modalActionBtn');

            openBtn.addEventListener('click', function() {
                logEvent('Opening modal');
                modal.style.display = 'block';
            });

            closeBtn.addEventListener('click', function() {
                logEvent('Closing modal');
                modal.style.display = 'none';
            });

            actionBtn.addEventListener('click', function() {
                logEvent('Modal action button clicked');
            });

            // Close modal when clicking outside
            window.addEventListener('click', function(e) {
                if (e.target === modal) {
                    logEvent('Closing modal (clicked outside)');
                    modal.style.display = 'none';
                }
            });
        }

        // Test 4: Dynamic content
        let contentCounter = 0;
        
        function setupDynamicContent() {
            document.getElementById('addContentBtn').addEventListener('click', function() {
                contentCounter++;
                const container = document.getElementById('dynamicContent');
                const newDiv = document.createElement('div');
                newDiv.innerHTML = `
                    <p>Dynamic content ${contentCounter} 
                    <button class="test-button dynamic-btn" data-id="${contentCounter}">
                        Dynamic Button ${contentCounter}
                    </button></p>
                `;
                container.appendChild(newDiv);
                logEvent(`Added dynamic content ${contentCounter}`);
            });

            document.getElementById('removeContentBtn').addEventListener('click', function() {
                const container = document.getElementById('dynamicContent');
                if (container.children.length > 0) {
                    container.removeChild(container.lastChild);
                    logEvent('Removed last dynamic content');
                }
            });

            // Event delegation for dynamic buttons
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('dynamic-btn')) {
                    const id = e.target.getAttribute('data-id');
                    logEvent(`Dynamic button ${id} clicked via delegation`);
                }
            });
        }

        // Clear log function
        function clearLog() {
            document.getElementById('eventLog').innerHTML = '';
            logEvent('Log cleared');
        }

        // Initialize everything
        document.addEventListener('DOMContentLoaded', function() {
            logEvent('Page loaded - initializing tests');
            
            setupEventListeners();
            setupModal();
            setupDynamicContent();
            
            document.getElementById('addListenersBtn').addEventListener('click', addBadListeners);
            document.getElementById('clearLogBtn').addEventListener('click', clearLog);
            
            logEvent('All tests initialized');
        });

        // Test for multiple DOMContentLoaded (this would be bad)
        document.addEventListener('DOMContentLoaded', function() {
            logEvent('WARNING: Second DOMContentLoaded listener fired!');
        });
    </script>
</body>
</html>
