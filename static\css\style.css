/* Custom CSS for TCG Tools */

:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #059669;
    --danger-color: #dc2626;
    --warning-color: #d97706;
    --info-color: #0891b2;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    --border-radius: 0.75rem;
    --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --transition: all 0.3s ease;
}

/* Global Styles */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f1f5f9;
    line-height: 1.6;
    margin: 0;
    padding: 0;
}

.main-content {
    min-height: calc(100vh - 200px);
    padding: 0;
    margin: 0;
}

/* Navigation */
.navbar {
    box-shadow: var(--box-shadow);
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%) !important;
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: var(--transition);
    border-radius: 0.5rem;
    margin: 0 0.25rem;
    padding: 0.5rem 1rem !important;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

/* Hero Section */
.hero-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;
    min-height: auto;
    display: flex;
    align-items: center;
    margin-top: 0;
    padding: 3rem 0;
}

.hero-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.min-vh-50 {
    min-height: 50vh;
}

.hero-content {
    position: relative;
    z-index: 2;
    color: white;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
}

.hero-badge .badge {
    font-size: 0.9rem;
    font-weight: 600;
    border-radius: 50px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.2;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
}

.text-gradient {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    opacity: 0.9;
    line-height: 1.6;
    margin-bottom: 2rem;
}

.hero-stats {
    margin: 0 -15px;
}

.stat-item {
    padding: 0 15px;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #ffd700;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 0;
}

/* Workflow Section */
.workflow-section {
    position: relative;
    z-index: 2;
    padding: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
}

.workflow-title {
    color: white;
    font-size: 2rem;
    font-weight: 700;
    text-align: left;
    margin-bottom: 2rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.workflow-steps {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.workflow-step {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 1.25rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition);
}

.workflow-step:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateX(5px);
}

.step-number {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: #1e293b;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.1rem;
    flex-shrink: 0;
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.step-content {
    flex: 1;
}

.step-title {
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    line-height: 1.3;
}

.step-description {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 0;
}

/* Cards */
.card {
    border: none;
    border-radius: var(--border-radius);
    transition: var(--transition);
    overflow: hidden;
}

.card-hover:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.card-title {
    font-weight: 600;
    color: var(--dark-color);
}

.card-text {
    font-size: 0.9rem;
}

/* Buttons */
.btn {
    border-radius: 0.5rem;
    font-weight: 500;
    transition: var(--transition);
    border: none;
    padding: 0.75rem 1.5rem;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    box-shadow: 0 2px 4px rgba(37, 99, 235, 0.3);
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(37, 99, 235, 0.4);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    transform: translateY(-1px);
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

/* Section Titles */
.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.1rem;
    color: var(--secondary-color);
    margin-bottom: 0;
}

/* Search Section */
.search-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.search-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    padding: 1.5rem 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.search-icon {
    font-size: 1.5rem;
}

.search-body {
    padding: 2rem;
}

.search-input-wrapper {
    position: relative;
    margin-bottom: 1.5rem;
}

.search-input {
    width: 100%;
    padding: 1rem 1.5rem;
    border: 2px solid #e2e8f0;
    border-radius: 15px;
    font-size: 1.1rem;
    transition: var(--transition);
    background: #f8fafc;
    padding-right: 60px;
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    background: white;
    box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1);
}

.search-btn {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 44px;
    height: 44px;
    border: none;
    background: var(--primary-color);
    color: white;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    cursor: pointer;
}

.search-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-50%) scale(1.05);
}

.search-suggestions {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.suggestion-tag {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    color: var(--secondary-color);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    border: 1px solid transparent;
}

.suggestion-tag:hover {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

/* Dropdown Filters */
.dropdown-filters {
    margin-bottom: 1rem;
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.dropdown-select {
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: var(--transition);
    background: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.dropdown-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    outline: none;
}

.dropdown-select:disabled {
    background-color: #f8fafc;
    color: #94a3b8;
    cursor: not-allowed;
}

.search-actions {
    text-align: center;
    padding-top: 1rem;
    border-top: 1px solid #e2e8f0;
}

.search-actions .btn {
    border-radius: 10px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
}

.quick-search-toggle {
    text-align: center;
    border-top: 1px solid #e2e8f0;
    padding-top: 1rem;
}

.quick-search-toggle .btn-link {
    text-decoration: none;
    font-size: 0.9rem;
    transition: var(--transition);
}

.quick-search-toggle .btn-link:hover {
    color: var(--primary-color) !important;
}

.traditional-search {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e2e8f0;
}

/* Card Details Section */
.card-details-section {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 2px solid #e2e8f0;
    animation: fadeInUp 0.3s ease-out;
}

/* Listings Table Styles */
.card-listings-section {
    padding: 0 1.5rem;
}

.section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.listings-table-container {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
    margin-bottom: 1.5rem;
}

.listings-table-container table {
    margin-bottom: 0;
}

.listings-table-container thead th {
    background-color: #f8fafc;
    font-weight: 600;
    color: var(--dark-color);
    font-size: 0.85rem;
    padding: 0.75rem 1rem;
    border-bottom: 2px solid #e2e8f0;
}

.listings-table-container tbody td {
    padding: 0.75rem 1rem;
    font-size: 0.85rem;
    vertical-align: middle;
}

.listings-table-container tbody tr:hover {
    background-color: #f1f5f9;
}

.card-details-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 10px 10px 0 0;
    margin-bottom: 0;
}

.card-details-header h5 {
    margin: 0;
    font-weight: 600;
}

.card-details-content {
    background: white;
    border: 2px solid #e2e8f0;
    border-top: none;
    border-radius: 0 0 10px 10px;
    padding: 0;
}

.card-details-flex {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    padding: 1.5rem;
}

.card-image-container {
    flex: 0 0 250px;
    max-width: 250px;
    margin: 0 auto;
}

.card-detail-image {
    width: 100%;
    height: auto;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    object-fit: contain;
}

.no-image-placeholder {
    width: 100%;
    height: 350px;
    background: #f1f5f9;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--secondary-color);
    font-style: italic;
    border: 2px dashed #e2e8f0;
}

.card-info-grid {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0;
}

.card-info-item {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #f1f5f9;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-info-item:nth-child(odd) {
    background-color: #f8fafc;
}

.card-info-item:nth-child(4) {
    border-bottom: none;
}

.card-prices-item {
    border-bottom: none !important;
}

.card-info-label {
    font-weight: 600;
    color: var(--dark-color);
    font-size: 0.9rem;
}

.card-info-value {
    color: var(--text-color);
    font-size: 0.9rem;
    text-align: right;
}

.card-price {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--success-color);
}

.card-prices-item {
    grid-column: 1 / -1;
}

.price-variant {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e2e8f0;
}

.price-variant:last-child {
    border-bottom: none;
}

.variant-name {
    font-weight: 600;
    color: var(--dark-color);
    font-size: 0.9rem;
}

.variant-price {
    font-weight: 700;
    color: var(--success-color);
    font-size: 1rem;
}

.no-price-data {
    color: var(--text-muted);
    font-style: italic;
    font-size: 0.9rem;
}

.card-actions {
    padding: 1.5rem;
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
    text-align: center;
}

.card-actions .btn {
    border-radius: 8px;
    padding: 0.75rem 2rem;
    font-weight: 600;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Form Controls */
.form-control {
    border: 2px solid #e2e8f0;
    border-radius: 0.5rem;
    transition: var(--transition);
    padding: 0.75rem 1rem;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Modal */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.modal-header {
    border-bottom: 2px solid #f1f5f9;
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 2px solid #f1f5f9;
    padding: 1.5rem;
}

/* Badges */
.badge {
    font-weight: 500;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
}

/* Loading Spinner */
.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Footer */
footer {
    background: linear-gradient(135deg, var(--dark-color) 0%, #0f172a 100%) !important;
    margin-top: auto;
}

footer a {
    transition: var(--transition);
}

footer a:hover {
    color: var(--primary-color) !important;
}

/* Alerts */
.alert {
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
    box-shadow: var(--box-shadow);
}

.alert-success {
    background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
    color: var(--success-color);
}

.alert-danger {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    color: var(--danger-color);
}

.alert-info {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    color: var(--info-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-gradient {
        min-height: auto;
        margin-top: 0;
        padding: 2rem 0;
        align-items: center;
    }

    .hero-content {
        padding: 0;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .hero-stats {
        margin-top: 2rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .workflow-section {
        padding: 0;
        margin-top: 2rem;
    }

    .workflow-title {
        font-size: 1.5rem;
        margin-bottom: 1.5rem;
        text-align: center;
    }

    .workflow-steps {
        gap: 1rem;
    }

    .workflow-step {
        padding: 1rem;
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }

    .workflow-step:hover {
        transform: none;
    }

    .step-number {
        width: 35px;
        height: 35px;
        font-size: 1rem;
        align-self: center;
    }

    .step-title {
        font-size: 1rem;
    }

    .step-description {
        font-size: 0.85rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .search-card {
        margin: 0 1rem;
    }

    .search-header {
        padding: 1rem 1.5rem;
    }

    .search-body {
        padding: 1.5rem;
    }

    .dropdown-filters .row {
        margin: 0;
    }

    .dropdown-filters .col-md-4 {
        padding: 0 0.5rem;
        margin-bottom: 1rem;
    }

    .search-actions .btn {
        width: 100%;
        margin: 0.25rem 0 !important;
    }

    .card-info-grid {
        grid-template-columns: 1fr;
    }

    .card-details-header {
        padding: 0.75rem 1rem;
    }

    .card-info-item {
        padding: 0.75rem 1rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }

    .card-info-value {
        text-align: left;
        font-weight: 600;
    }

    .card-actions {
        padding: 1rem;
    }

    .cards-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        padding: 0 1rem;
    }

    .modern-card:hover {
        transform: none;
    }

    .btn {
        padding: 0.625rem 1.25rem;
    }

    .load-more-btn {
        padding: 0.875rem 1.5rem;
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-stats .col-4 {
        margin-bottom: 1rem;
    }

    .search-suggestions {
        justify-content: center;
    }

    .suggestion-tag {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
    background: var(--secondary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Price Display */
.text-success {
    color: var(--success-color) !important;
    font-weight: 600;
}

/* Feature Cards */
.feature-card {
    position: relative;
    overflow: visible;
    transition: var(--transition);
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.feature-badge {
    position: absolute;
    top: -12px;
    right: 20px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    font-weight: 600;
    font-size: 0.85rem;
    padding: 0.35rem 1rem;
    border-radius: 20px;
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

/* Modern Cards Grid */
.cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.card-item {
    position: relative;
}

.modern-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    transition: var(--transition);
    border: 1px solid rgba(255, 255, 255, 0.2);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.modern-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.card-header-modern {
    padding: 1.5rem 1.5rem 0;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.card-type-badge {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: #1e293b;
    width: 40px;
    height: 40px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.card-actions {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    width: 36px;
    height: 36px;
    border: none;
    background: #f8fafc;
    color: var(--secondary-color);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    cursor: pointer;
}

.action-btn:hover {
    background: #fee2e2;
    color: #dc2626;
    transform: scale(1.1);
}

.card-content {
    padding: 1.5rem;
    flex-grow: 1;
}

.card-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 1rem;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.card-meta {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--secondary-color);
}

.meta-item i {
    width: 16px;
    text-align: center;
}

.card-price {
    background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
    padding: 1rem;
    border-radius: 12px;
    text-align: center;
    margin-bottom: 1rem;
}

.price-label {
    display: block;
    font-size: 0.8rem;
    color: var(--success-color);
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.price-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--success-color);
}

.card-footer-modern {
    padding: 0 1.5rem 1.5rem;
}

.notification-btn {
    width: 100%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    border: none;
    padding: 1rem;
    border-radius: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: var(--transition);
    cursor: pointer;
}

.notification-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(37, 99, 235, 0.4);
}

.notification-btn i {
    font-size: 1.1rem;
}

/* Load More Button */
.load-more-section {
    text-align: center;
    margin-top: 3rem;
}

.load-more-btn {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: var(--secondary-color);
    border: 2px solid #e2e8f0;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 1rem;
    transition: var(--transition);
    cursor: pointer;
    font-size: 1.1rem;
}

.load-more-btn:hover {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(37, 99, 235, 0.3);
}

.load-more-btn .btn-icon {
    transition: var(--transition);
}

.load-more-btn:hover .btn-icon {
    transform: translateY(2px);
}

/* No Results Styling */
#noResults .card {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 2px dashed #cbd5e1;
}

/* Notification Cards */
.notification-card {
    border-left: 4px solid var(--primary-color);
}

.notification-card.triggered {
    border-left-color: var(--success-color);
}

/* Pricing Section */
.pricing-card {
    position: relative;
    overflow: visible;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    height: 100%;
}

.pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.pricing-card.featured {
    border: 2px solid var(--primary-color);
    box-shadow: 0 15px 30px rgba(37, 99, 235, 0.2);
    z-index: 2;
}

.featured-badge {
    position: absolute;
    top: -12px;
    right: 20px;
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: var(--dark-color);
    font-weight: 600;
    font-size: 0.85rem;
    padding: 0.35rem 1rem;
    border-radius: 20px;
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.pricing-header {
    padding: 2rem 1.5rem;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 20px 20px 0 0;
    border-bottom: 1px solid #e2e8f0;
}

.pricing-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.pricing-value {
    margin: 1.5rem 0 1rem;
}

.price-currency {
    font-size: 1.5rem;
    font-weight: 600;
    vertical-align: top;
    margin-right: 2px;
    position: relative;
    top: 0.5rem;
}

.price-amount {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1;
}

.price-period {
    font-size: 1rem;
    color: var(--secondary-color);
}

.pricing-description {
    font-size: 0.95rem;
    color: var(--secondary-color);
    margin-bottom: 0;
}

.pricing-body {
    padding: 1.5rem;
    flex-grow: 1;
}

.pricing-features {
    list-style: none;
    padding: 0;
    margin: 0;
}

.feature-item {
    padding: 0.75rem 0;
    border-bottom: 1px solid #f1f5f9;
    display: flex;
    align-items: center;
}

.feature-item:last-child {
    border-bottom: none;
}

.feature-icon {
    color: var(--success-color);
    margin-right: 0.75rem;
    font-size: 1rem;
    flex-shrink: 0;
}

.feature-text {
    font-size: 0.95rem;
    color: var(--dark-color);
}

.pricing-footer {
    padding: 1.5rem;
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
    border-radius: 0 0 20px 20px;
}

.pricing-btn {
    padding: 0.75rem 2rem;
    font-weight: 600;
    width: 100%;
}

/* Utility Classes */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.shadow-hover {
    transition: var(--transition);
}

.shadow-hover:hover {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}
