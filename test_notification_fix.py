#!/usr/bin/env python3
"""
Test script to verify the notification fix
This script tests that creating a notification doesn't immediately send an email
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models import notification_model
from unittest.mock import patch
import json

def test_create_notification_no_immediate_email():
    """
    Test that creating a notification doesn't send an immediate email
    """
    print("Testing notification creation without immediate email...")
    
    # Mock the send_notification_email function to track if it's called
    with patch('models.send_notification_email') as mock_send_email:
        # Create a test notification
        notification_id = notification_model.create_notification(
            card_id="test_card_123",
            card_name="Test Card",
            target_price=10.99,
            user_email="<EMAIL>"
        )
        
        # Check if notification was created
        if notification_id:
            print(f"✅ Notification created successfully with ID: {notification_id}")
        else:
            print("❌ Failed to create notification")
            return False
        
        # Check if send_notification_email was called (it shouldn't be)
        if mock_send_email.called:
            print(f"❌ FAIL: send_notification_email was called {mock_send_email.call_count} times")
            print(f"   Call arguments: {mock_send_email.call_args_list}")
            return False
        else:
            print("✅ PASS: send_notification_email was NOT called (correct behavior)")
            return True

def test_notification_structure():
    """
    Test that the notification is created with correct structure
    """
    print("\nTesting notification structure...")
    
    # Create a test notification
    notification_id = notification_model.create_notification(
        card_id="test_card_456",
        card_name="Another Test Card",
        target_price=15.50,
        user_email="<EMAIL>"
    )
    
    if not notification_id:
        print("❌ Failed to create notification")
        return False
    
    # Get the notification from database
    notifications = notification_model.get_user_notifications("<EMAIL>")
    
    if not notifications:
        print("❌ No notifications found")
        return False
    
    # Find our notification
    test_notification = None
    for notif in notifications:
        if str(notif.get("_id")) == notification_id:
            test_notification = notif
            break
    
    if not test_notification:
        print("❌ Test notification not found")
        return False
    
    # Check notification structure
    required_fields = ["card_id", "card_name", "target_price", "user_email", "created_at", "is_active", "triggered"]
    
    for field in required_fields:
        if field not in test_notification:
            print(f"❌ Missing required field: {field}")
            return False
    
    # Check field values
    if test_notification["card_id"] != "test_card_456":
        print(f"❌ Wrong card_id: {test_notification['card_id']}")
        return False
    
    if test_notification["card_name"] != "Another Test Card":
        print(f"❌ Wrong card_name: {test_notification['card_name']}")
        return False
    
    if test_notification["target_price"] != 15.50:
        print(f"❌ Wrong target_price: {test_notification['target_price']}")
        return False
    
    if test_notification["user_email"] != "<EMAIL>":
        print(f"❌ Wrong user_email: {test_notification['user_email']}")
        return False
    
    if test_notification["is_active"] != True:
        print(f"❌ Wrong is_active: {test_notification['is_active']}")
        return False
    
    if test_notification["triggered"] != False:
        print(f"❌ Wrong triggered: {test_notification['triggered']}")
        return False
    
    print("✅ PASS: Notification structure is correct")
    print(f"   Notification: {json.dumps({k: str(v) for k, v in test_notification.items()}, indent=2)}")
    return True

def cleanup_test_notifications():
    """
    Clean up test notifications
    """
    print("\nCleaning up test notifications...")
    
    # Get test notifications
    test_notifications = notification_model.get_user_notifications("<EMAIL>")
    test_notifications.extend(notification_model.get_user_notifications("<EMAIL>"))
    
    deleted_count = 0
    for notif in test_notifications:
        if notification_model.delete_notification(str(notif["_id"])):
            deleted_count += 1
    
    print(f"✅ Cleaned up {deleted_count} test notifications")

def main():
    """
    Run all tests
    """
    print("🧪 Testing Notification Fix")
    print("=" * 50)
    
    try:
        # Run tests
        test1_passed = test_create_notification_no_immediate_email()
        test2_passed = test_notification_structure()
        
        # Cleanup
        cleanup_test_notifications()
        
        # Summary
        print("\n" + "=" * 50)
        print("📊 Test Results:")
        print(f"   Create notification without email: {'✅ PASS' if test1_passed else '❌ FAIL'}")
        print(f"   Notification structure: {'✅ PASS' if test2_passed else '❌ FAIL'}")
        
        if test1_passed and test2_passed:
            print("\n🎉 All tests passed! The notification fix is working correctly.")
            print("   ✅ No immediate emails are sent when creating alerts")
            print("   ✅ Notifications are properly structured and stored")
            return 0
        else:
            print("\n❌ Some tests failed. Please check the implementation.")
            return 1
            
    except Exception as e:
        print(f"\n💥 Error running tests: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
