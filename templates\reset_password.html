{% extends "base.html" %}

{% block title %}TCG Alert - Reset Password{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0"><i class="fas fa-lock me-2"></i>Create New Password</h3>
                </div>
                <div class="card-body p-4">
                    <div class="alert alert-danger d-none" id="resetError"></div>
                    <div class="alert alert-success d-none" id="resetSuccess"></div>
                    <p class="mb-4">Enter your new password below.</p>
                    <form id="resetPasswordForm">
                        <input type="hidden" id="resetToken" name="resetToken" value="{{ token }}">
                        <div class="mb-3">
                            <label for="password" class="form-label">New Password</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                            <div class="form-text">Password must be at least 8 characters long.</div>
                        </div>
                        <div class="mb-3">
                            <label for="confirmPassword" class="form-label">Confirm New Password</label>
                            <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" required>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Reset Password
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-footer bg-light text-center">
                    <p class="mb-0">Remember your password? <a href="/login" class="text-decoration-none">Back to Login</a></p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const resetPasswordForm = document.getElementById('resetPasswordForm');
        const resetError = document.getElementById('resetError');
        const resetSuccess = document.getElementById('resetSuccess');

        resetPasswordForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Clear previous messages
            resetError.classList.add('d-none');
            resetSuccess.classList.add('d-none');
            
            // Get form data
            const token = document.getElementById('resetToken').value;
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            
            // Validate form
            if (!password || !confirmPassword) {
                resetError.textContent = 'Please fill in all fields';
                resetError.classList.remove('d-none');
                return;
            }
            
            if (password.length < 8) {
                resetError.textContent = 'Password must be at least 8 characters long';
                resetError.classList.remove('d-none');
                return;
            }
            
            if (password !== confirmPassword) {
                resetError.textContent = 'Passwords do not match';
                resetError.classList.remove('d-none');
                return;
            }
            
            // Send password reset request
            fetch('/api/reset-password', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    token: token,
                    password: password
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    resetError.textContent = data.error;
                    resetError.classList.remove('d-none');
                } else {
                    // Show success message
                    resetSuccess.textContent = data.message || 'Password reset successful! You can now log in with your new password.';
                    resetSuccess.classList.remove('d-none');
                    
                    // Clear form
                    resetPasswordForm.reset();
                    
                    // Redirect to login page after a delay
                    setTimeout(() => {
                        window.location.href = '/login';
                    }, 5000);
                }
            })
            .catch(error => {
                resetError.textContent = 'An error occurred. Please try again.';
                resetError.classList.remove('d-none');
                console.error('Error:', error);
            });
        });
    });
</script>
{% endblock %}
