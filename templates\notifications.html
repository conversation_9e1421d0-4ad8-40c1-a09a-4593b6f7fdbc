{% extends "base.html" %}

{% block title %}My Notifications - TCG Tools{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="fw-bold">
                <i class="fas fa-bell me-2 text-primary"></i>My Price Alerts
            </h2>
            <p class="text-muted mb-0">Manage your price notifications and track your alerts</p>
        </div>
        <div>
            <a href="/dashboard" class="btn btn-outline-secondary me-2">
                <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
            </a>
            <a href="/dashboard" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>Add New Alert
            </a>
        </div>
    </div>

    <!-- View Toggle and Stats -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <label for="emailFilter" class="form-label">Filter by Email:</label>
                            <div class="input-group">
                                <input type="email" class="form-control" id="emailFilter"
                                       placeholder="Enter email to filter notifications">
                                <button class="btn btn-primary" type="button" id="filterBtn">
                                    <i class="fas fa-filter"></i>
                                </button>
                                <button class="btn btn-outline-secondary" type="button" id="clearFilterBtn">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex justify-content-end align-items-center">
                                <div class="me-3">
                                    <small class="text-muted">View:</small>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-primary active" id="tableViewBtn">
                                            <i class="fas fa-table"></i> Table
                                        </button>
                                        <button type="button" class="btn btn-outline-primary" id="cardViewBtn">
                                            <i class="fas fa-th-large"></i> Cards
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-body text-center">
                    <div class="row">
                        <div class="col-6">
                            <h5 class="mb-0 text-primary" id="activeCount">0</h5>
                            <small class="text-muted">Active</small>
                        </div>
                        <div class="col-6">
                            <h5 class="mb-0 text-success" id="triggeredCount">0</h5>
                            <small class="text-muted">Triggered</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Spinner -->
    <div id="loadingSpinner" class="text-center d-none">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-2 text-muted">Loading notifications...</p>
    </div>

    <!-- Notifications List -->
    <div id="notificationsContainer">
        <!-- Table View -->
        <div id="tableView" class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Card Name</th>
                                <th>Print Type</th>
                                <th>Target Price</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="notificationsTableBody">
                            <!-- Table rows will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Card View -->
        <div id="cardView" class="d-none">
            <div id="notificationsList" class="row">
                <!-- Notification cards will be loaded here -->
            </div>
        </div>
    </div>

    <!-- No Notifications Message -->
    <div id="noNotifications" class="text-center d-none">
        <div class="card">
            <div class="card-body py-5">
                <i class="fas fa-bell-slash text-muted mb-3" style="font-size: 3rem;"></i>
                <h4 class="text-muted">No notifications found</h4>
                <p class="text-muted">You haven't set up any price notifications yet.</p>
                <a href="{{ url_for('index') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>Create Your First Notification
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Edit Notification Modal -->
<div class="modal fade" id="editModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2 text-primary"></i>Edit Price Alert
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editNotificationForm">
                    <div class="mb-3">
                        <label class="form-label">Card Name</label>
                        <input type="text" class="form-control" id="editCardName" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Print Type</label>
                        <input type="text" class="form-control" id="editPrinting" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="editTargetPrice" class="form-label">Target Price</label>
                        <div class="input-group">
                            <span class="input-group-text">$</span>
                            <input type="number" class="form-control" id="editTargetPrice"
                                   step="0.01" min="0.01" required>
                        </div>
                        <div class="form-text">Enter your maximum price for this card</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveEditBtn">
                    <i class="fas fa-save me-1"></i>Save Changes
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-trash me-2 text-danger"></i>Delete Notification
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this notification?</p>
                <p class="text-muted small">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash me-1"></i>Delete
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    let currentFilter = '';
    let notificationToDelete = null;
    let notificationToEdit = null;
    let currentView = 'table'; // Default to table view

    // Initialize the page
    document.addEventListener('DOMContentLoaded', function() {
        setupNotificationsEventListeners();
        loadNotifications();

        // Auto-fill email filter if user is logged in
        fetch('/api/user')
            .then(response => response.json())
            .then(userData => {
                if (userData.success && userData.user && userData.user.email) {
                    document.getElementById('emailFilter').value = userData.user.email;
                    currentFilter = userData.user.email;
                    loadNotifications();
                }
            })
            .catch(error => {
                console.error('Error getting user data:', error);
            });
    });

    function setupNotificationsEventListeners() {
        // Filter button
        document.getElementById('filterBtn').addEventListener('click', function() {
            currentFilter = document.getElementById('emailFilter').value;
            loadNotifications();
        });

        // Clear filter button
        document.getElementById('clearFilterBtn').addEventListener('click', function() {
            document.getElementById('emailFilter').value = '';
            currentFilter = '';
            loadNotifications();
        });

        // Enter key in email filter
        document.getElementById('emailFilter').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                currentFilter = this.value;
                loadNotifications();
            }
        });

        // View toggle buttons
        document.getElementById('tableViewBtn').addEventListener('click', function() {
            switchView('table');
        });

        document.getElementById('cardViewBtn').addEventListener('click', function() {
            switchView('card');
        });

        // Delete confirmation
        document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
            if (notificationToDelete) {
                deleteNotification(notificationToDelete);
            }
        });

        // Edit confirmation
        document.getElementById('saveEditBtn').addEventListener('click', function() {
            if (notificationToEdit) {
                saveEditedNotification();
            }
        });
    }

    function switchView(view) {
        currentView = view;

        // Update button states
        document.getElementById('tableViewBtn').classList.toggle('active', view === 'table');
        document.getElementById('cardViewBtn').classList.toggle('active', view === 'card');

        // Show/hide views
        document.getElementById('tableView').classList.toggle('d-none', view !== 'table');
        document.getElementById('cardView').classList.toggle('d-none', view !== 'card');
    }

    function loadNotifications() {
        showLoading();
        
        let url = '/api/notifications';
        if (currentFilter) {
            url += `?email=${encodeURIComponent(currentFilter)}`;
        }

        fetch(url)
            .then(response => response.json())
            .then(notifications => {
                hideLoading();
                displayNotifications(notifications);
            })
            .catch(error => {
                hideLoading();
                console.error('Error loading notifications:', error);
                showAlert('Error loading notifications. Please try again.', 'danger');
            });
    }

    function displayNotifications(notifications) {
        const tableBody = document.getElementById('notificationsTableBody');
        const cardContainer = document.getElementById('notificationsList');
        const noNotifications = document.getElementById('noNotifications');

        if (notifications.length === 0) {
            tableBody.innerHTML = '';
            cardContainer.innerHTML = '';
            noNotifications.classList.remove('d-none');
            return;
        }

        noNotifications.classList.add('d-none');

        // Update stats
        const activeCount = notifications.filter(n => !n.triggered).length;
        const triggeredCount = notifications.filter(n => n.triggered).length;
        document.getElementById('activeCount').textContent = activeCount;
        document.getElementById('triggeredCount').textContent = triggeredCount;

        // Display table view
        displayTableView(notifications, tableBody);

        // Display card view
        displayCardView(notifications, cardContainer);
    }

    function displayTableView(notifications, tableBody) {
        tableBody.innerHTML = notifications.map(notification => {
            const createdDate = new Date(notification.created_at).toLocaleDateString();
            const printing = notification.printing || 'Normal';
            const statusBadge = notification.triggered
                ? '<span class="badge bg-success">Triggered</span>'
                : '<span class="badge bg-primary">Active</span>';

            return `
                <tr>
                    <td>
                        <div class="d-flex flex-column">
                            <span class="fw-medium">${notification.card_name}</span>
                            ${notification.product_id ? `<small class="text-muted">ID: ${notification.product_id}</small>` : ''}
                        </div>
                    </td>
                    <td>
                        <span class="badge bg-secondary">${printing}</span>
                    </td>
                    <td>
                        <span class="fw-bold text-success">$${notification.target_price.toFixed(2)}</span>
                    </td>
                    <td>${statusBadge}</td>
                    <td>
                        <small>${createdDate}</small>
                    </td>
                    <td>
                        <div class="btn-group btn-group-sm" role="group">
                            <button class="btn btn-outline-primary edit-notification-btn"
                                    data-notification-id="${notification._id}"
                                    data-card-name="${notification.card_name}"
                                    data-printing="${printing}"
                                    data-target-price="${notification.target_price}"
                                    title="Edit notification">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-danger delete-notification-btn"
                                    data-notification-id="${notification._id}"
                                    title="Delete notification">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');

        // Add event listeners to edit and delete buttons
        tableBody.querySelectorAll('.edit-notification-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                notificationToEdit = this.dataset.notificationId;
                document.getElementById('editCardName').value = this.dataset.cardName;
                document.getElementById('editPrinting').value = this.dataset.printing;
                document.getElementById('editTargetPrice').value = this.dataset.targetPrice;

                const editModal = new bootstrap.Modal(document.getElementById('editModal'));
                editModal.show();
            });
        });

        tableBody.querySelectorAll('.delete-notification-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                notificationToDelete = this.dataset.notificationId;
                const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
                deleteModal.show();
            });
        });
    }

    function displayCardView(notifications, cardContainer) {
        cardContainer.innerHTML = notifications.map(notification => {
            const createdDate = new Date(notification.created_at).toLocaleDateString();
            const printing = notification.printing || 'Normal';

            return `
                <div class="col-lg-6 col-md-8 mb-3">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="card-title text-truncate" title="${notification.card_name}">
                                    ${notification.card_name}
                                </h6>
                                <div class="btn-group btn-group-sm" role="group">
                                    <button class="btn btn-outline-primary edit-notification-btn"
                                            data-notification-id="${notification._id}"
                                            data-card-name="${notification.card_name}"
                                            data-printing="${printing}"
                                            data-target-price="${notification.target_price}"
                                            title="Edit notification">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-danger delete-notification-btn"
                                            data-notification-id="${notification._id}"
                                            title="Delete notification">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="mb-2">
                                <span class="badge bg-secondary me-2">${printing}</span>
                                <span class="badge ${notification.triggered ? 'bg-success' : 'bg-primary'}">
                                    ${notification.triggered ? 'Triggered' : 'Active'}
                                </span>
                            </div>

                            <p class="card-text">
                                <strong class="text-success">Target Price: $${notification.target_price.toFixed(2)}</strong>
                            </p>

                            ${notification.product_id ? `
                                <p class="card-text text-muted small">
                                    <i class="fas fa-barcode me-1"></i>Product ID: ${notification.product_id}
                                </p>
                            ` : ''}

                            ${notification.user_email ? `
                                <p class="card-text text-muted small">
                                    <i class="fas fa-envelope me-1"></i>${notification.user_email}
                                </p>
                            ` : ''}

                            <p class="card-text text-muted small">
                                <i class="fas fa-calendar me-1"></i>Created: ${createdDate}
                            </p>
                        </div>
                    </div>
                </div>
            `;
        }).join('');

        // Add event listeners to edit and delete buttons
        cardContainer.querySelectorAll('.edit-notification-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                notificationToEdit = this.dataset.notificationId;
                document.getElementById('editCardName').value = this.dataset.cardName;
                document.getElementById('editPrinting').value = this.dataset.printing;
                document.getElementById('editTargetPrice').value = this.dataset.targetPrice;

                const editModal = new bootstrap.Modal(document.getElementById('editModal'));
                editModal.show();
            });
        });

        cardContainer.querySelectorAll('.delete-notification-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                notificationToDelete = this.dataset.notificationId;
                const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
                deleteModal.show();
            });
        });
    }

    function deleteNotification(notificationId) {
        fetch(`/api/notifications/${notificationId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('Notification deleted successfully!', 'success');
                loadNotifications();
            } else {
                showAlert('Error deleting notification. Please try again.', 'danger');
            }
        })
        .catch(error => {
            console.error('Error deleting notification:', error);
            showAlert('Error deleting notification. Please try again.', 'danger');
        })
        .finally(() => {
            const deleteModal = bootstrap.Modal.getInstance(document.getElementById('deleteModal'));
            deleteModal.hide();
            notificationToDelete = null;
        });
    }

    function saveEditedNotification() {
        const newTargetPrice = parseFloat(document.getElementById('editTargetPrice').value);

        if (!newTargetPrice || newTargetPrice <= 0) {
            showAlert('Please enter a valid target price.', 'danger');
            return;
        }

        // Disable save button and show loading
        const saveBtn = document.getElementById('saveEditBtn');
        const originalText = saveBtn.innerHTML;
        saveBtn.disabled = true;
        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Saving...';

        // Update the notification
        fetch(`/api/notifications/${notificationToEdit}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                target_price: newTargetPrice
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('Notification updated successfully!', 'success');
                loadNotifications(); // Reload the notifications list
            } else {
                showAlert('Error updating notification. Please try again.', 'danger');
            }
        })
        .catch(error => {
            console.error('Error updating notification:', error);
            showAlert('Error updating notification. Please try again.', 'danger');
        })
        .finally(() => {
            // Re-enable save button
            saveBtn.disabled = false;
            saveBtn.innerHTML = originalText;

            // Close modal
            const editModal = bootstrap.Modal.getInstance(document.getElementById('editModal'));
            editModal.hide();
            notificationToEdit = null;
        });
    }

    function showLoading() {
        document.getElementById('loadingSpinner').classList.remove('d-none');
        document.getElementById('notificationsContainer').classList.add('d-none');
    }

    function hideLoading() {
        document.getElementById('loadingSpinner').classList.add('d-none');
        document.getElementById('notificationsContainer').classList.remove('d-none');
    }

    function showAlert(message, type) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const container = document.querySelector('.container');
        container.insertBefore(alertDiv, container.firstChild);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
</script>
{% endblock %}
