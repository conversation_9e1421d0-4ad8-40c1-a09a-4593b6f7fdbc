#!/usr/bin/env python3
"""
Daily Expansion Value Calculator - Optimized Version

This script calculates the total value for all expansions across all games
and stores the results in MongoDB with daily timestamps for historical tracking.

Optimizations:
- Multithreading for parallel processing
- Batch database queries for prices
- Optimized MongoDB queries
- Bulk database operations
- Price caching

The calculation includes:
- One of each print type (Normal, Foil, etc.) for every individual card
- Excludes sealed products (booster boxes, theme decks, etc.)
- Tracks highest value card per print type
- Stores daily snapshots for historical analysis

Usage:
    python calculate_expansion_values.py

Schedule this script to run daily via cron job or task scheduler.
"""

import os
import sys
from datetime import datetime, timezone, timedelta
from pymongo import MongoClient, UpdateOne
from bson import ObjectId
import logging
import threading
import queue
import time
from collections import defaultdict

# Add the current directory to Python path to import models
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models import Database, CardModel
from config import Config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('expansion_values.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Number of worker threads to use for parallel processing
# Adjust based on your CPU cores
NUM_WORKERS = max(1, os.cpu_count() * 2) if os.cpu_count() else 8

class ExpansionValueCalculator:
    def __init__(self):
        """Initialize the calculator with database connections."""
        try:
            # Initialize MongoDB connection
            self.client = MongoClient(Config.MONGO_URI)
            self.db = self.client[Config.MONGO_DB]
            
            # Initialize collections
            self.cards_collection = self.db['catalog']  # Cards are stored in catalog collection
            self.prices_collection = self.db['prices']
            self.expansion_values_collection = self.db['expansion_values']

            # Initialize Database and CardModel for price fetching
            self.database = Database()
            self.card_model = CardModel(self.database)

            # Define the allowed categoryId values for trading card games (same as CardModel)
            self.allowed_categories = [1, 2, 3, 13, 16, 17, 19, 20, 21, 23, 24, 25, 26, 27, 28, 36, 37, 38, 47, 48, 53, 54, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81]

            # Price cache to avoid repeated database queries
            self.price_cache = {}
            self.price_cache_lock = threading.Lock()
            
            # Results queue for thread-safe collection of results
            self.results_queue = queue.Queue()
            
            logger.info("Successfully connected to MongoDB")
            
        except Exception as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            raise

    def get_category_filter(self):
        """Get the category filter for trading card games."""
        return {"categoryId": {"$in": self.allowed_categories}}

    def get_all_expansions(self):
        """Get all unique game/expansion combinations from the cards collection."""
        try:
            # Get distinct combinations of gameName and expansionName
            # Only include cards that are not sealed products and are in allowed categories
            category_filter = self.get_category_filter()
            pipeline = [
                {
                    "$match": {
                        "$and": [
                            category_filter,
                            {"isSealed": {"$ne": True}},  # Exclude sealed products
                            {"gameName": {"$exists": True, "$ne": None}},
                            {"expansionName": {"$exists": True, "$ne": None}}
                        ]
                    }
                },
                {
                    "$group": {
                        "_id": {
                            "gameName": "$gameName",
                            "expansionName": "$expansionName"
                        }
                    }
                },
                {
                    "$project": {
                        "_id": 0,
                        "gameName": "$_id.gameName",
                        "expansionName": "$_id.expansionName"
                    }
                },
                {
                    "$sort": {
                        "gameName": 1,
                        "expansionName": 1
                    }
                }
            ]
            
            expansions = list(self.cards_collection.aggregate(pipeline))
            logger.info(f"Found {len(expansions)} unique expansions to process")
            return expansions
            
        except Exception as e:
            logger.error(f"Error getting expansions: {e}")
            return []

    def get_cards_for_expansion(self, game_name, expansion_name):
        """Get all cards for a specific expansion with optimized query."""
        try:
            # Only fetch the fields we need to reduce memory usage
            category_filter = self.get_category_filter()
            cards = list(self.cards_collection.find(
                {
                    "$and": [
                        category_filter,
                        {"gameName": game_name},
                        {"expansionName": expansion_name},
                        {"isSealed": {"$ne": True}}  # Exclude sealed products
                    ]
                },
                {
                    "productId": 1, 
                    "name": 1
                }
            ))
            
            return cards
        except Exception as e:
            logger.error(f"Error getting cards for {game_name} - {expansion_name}: {e}")
            return []

    def batch_get_prices(self, product_ids):
        """Get prices for multiple products in a single batch query."""
        if not product_ids:
            return {}
            
        # Check cache first (thread-safe)
        cached_results = {}
        missing_ids = []
        
        with self.price_cache_lock:
            for product_id in product_ids:
                if product_id in self.price_cache:
                    cached_results[product_id] = self.price_cache[product_id]
                else:
                    missing_ids.append(product_id)
        
        # If all results are cached, return immediately
        if not missing_ids:
            return cached_results
            
        try:
            # Convert all IDs to integers for the query
            int_product_ids = [int(pid) for pid in missing_ids if pid]
            
            # Query the database for all missing product IDs at once
            price_records = list(self.prices_collection.find(
                {"productId": {"$in": int_product_ids}},
                {"productId": 1, "prices": 1}
            ))
            
            # Process the results
            batch_results = {}
            for record in price_records:
                product_id = record.get('productId')
                if not product_id:
                    continue
                    
                variants_with_prices = []
                prices = record.get('prices', {})
                
                for variant_name, variant_data in prices.items():
                    if (variant_data and
                        isinstance(variant_data, dict) and
                        'lowPrice' in variant_data and
                        variant_data['lowPrice'] is not None):
                        
                        variants_with_prices.append({
                            'variant': variant_name,
                            'lowPrice': float(variant_data['lowPrice'])
                        })
                
                # Store in batch results
                batch_results[product_id] = variants_with_prices
            
            # Update cache (thread-safe)
            with self.price_cache_lock:
                for pid, variants in batch_results.items():
                    self.price_cache[pid] = variants
                
                # For any missing IDs that weren't found, add empty lists to cache
                for pid in missing_ids:
                    if pid not in batch_results:
                        self.price_cache[pid] = []
                        batch_results[pid] = []
            
            # Combine cached and new results
            results = {**cached_results, **batch_results}
            return results
            
        except Exception as e:
            logger.error(f"Error batch fetching prices: {e}")
            # Return cached results plus empty lists for missing IDs
            for pid in missing_ids:
                if pid not in cached_results:
                    cached_results[pid] = []
            return cached_results

    def calculate_expansion_value(self, expansion):
        """Calculate the total value for a specific expansion."""
        game_name = expansion['gameName']
        expansion_name = expansion['expansionName']
        
        try:
            logger.info(f"Calculating value for {game_name} - {expansion_name}")
            
            # Get all cards for this expansion
            cards = self.get_cards_for_expansion(game_name, expansion_name)
            
            if not cards:
                logger.warning(f"No cards found for {game_name} - {expansion_name}")
                return None
            
            # Extract product IDs for batch price query
            product_ids = [card.get('productId') for card in cards if card.get('productId')]
            
            # Batch fetch all prices at once
            all_prices = self.batch_get_prices(product_ids)
            
            # Process the results
            total_value = 0.0
            cards_with_prices = 0
            cards_without_prices = 0
            total_variants = 0
            variant_breakdown = defaultdict(lambda: {'count': 0, 'total_value': 0.0, 'highest_card': {'name': '', 'price': 0.0}})
            
            for card in cards:
                product_id = card.get('productId')
                if not product_id:
                    cards_without_prices += 1
                    continue
                
                price_variants = all_prices.get(product_id, [])
                
                if price_variants:
                    # Add the price of each variant (Normal, Foil, etc.)
                    for variant in price_variants:
                        variant_name = variant['variant']
                        variant_price = variant['lowPrice']
                        total_value += variant_price
                        total_variants += 1
                        
                        # Track variant breakdown
                        variant_breakdown[variant_name]['count'] += 1
                        variant_breakdown[variant_name]['total_value'] += variant_price
                        
                        # Track highest value card for this variant
                        if variant_price > variant_breakdown[variant_name]['highest_card']['price']:
                            variant_breakdown[variant_name]['highest_card'] = {
                                'name': card.get('name', 'Unknown Card'),
                                'price': variant_price
                            }
                    
                    cards_with_prices += 1
                else:
                    cards_without_prices += 1
            
            # Convert defaultdict to regular dict for MongoDB storage
            variant_breakdown_dict = {k: dict(v) for k, v in variant_breakdown.items()}
            
            result = {
                'gameName': game_name,
                'expansionName': expansion_name,
                'totalValue': round(total_value, 2),
                'cardsWithPrices': cards_with_prices,
                'cardsWithoutPrices': cards_without_prices,
                'totalCards': len(cards),
                'totalVariants': total_variants,
                'variantBreakdown': variant_breakdown_dict,
                'calculatedAt': datetime.now(timezone.utc),
                'date': datetime.now(timezone.utc).strftime('%Y-%m-%d')
            }
            
            logger.info(f"Calculated {game_name} - {expansion_name}: ${total_value:.2f} ({total_variants} variants)")
            return result
            
        except Exception as e:
            logger.error(f"Error calculating expansion value for {game_name} - {expansion_name}: {e}")
            return None

    def bulk_save_expansion_values(self, expansion_data_list):
        """Save multiple expansion values to MongoDB in a single bulk operation."""
        if not expansion_data_list:
            return
            
        try:
            # Prepare bulk operations
            bulk_operations = []
            
            for expansion_data in expansion_data_list:
                # Create a unique identifier for this expansion on this date
                expansion_data['_id'] = f"{expansion_data['gameName']}_{expansion_data['expansionName']}_{expansion_data['date']}"
                
                # Add to bulk operations list
                bulk_operations.append(
                    UpdateOne(
                        {'_id': expansion_data['_id']},
                        {'$set': expansion_data},
                        upsert=True
                    )
                )
            
            # Execute bulk operation
            if bulk_operations:
                result = self.expansion_values_collection.bulk_write(bulk_operations)
                logger.info(f"Bulk saved {len(expansion_data_list)} expansion values: {result.upserted_count} inserted, {result.modified_count} updated")
                
        except Exception as e:
            logger.error(f"Error bulk saving expansion values: {e}")

    def worker_thread(self, work_queue, results_lock, processed_count, error_count):
        """Worker thread function to process expansions from the queue."""
        while True:
            try:
                # Get an expansion from the queue
                expansion = work_queue.get(block=False)
                
                # Process the expansion
                expansion_data = self.calculate_expansion_value(expansion)
                
                # If successful, add to results and update counters
                if expansion_data:
                    # Add to results queue for bulk saving
                    self.results_queue.put(expansion_data)
                    
                    # Update counters (thread-safe)
                    with results_lock:
                        processed_count[0] += 1
                else:
                    # Update error counter (thread-safe)
                    with results_lock:
                        error_count[0] += 1
                
                # Mark task as done
                work_queue.task_done()
                
            except queue.Empty:
                # No more work
                break
            except Exception as e:
                # Handle any other exceptions
                logger.error(f"Error in worker thread: {e}")
                
                # Update error counter (thread-safe)
                with results_lock:
                    error_count[0] += 1
                
                # Mark task as done
                try:
                    work_queue.task_done()
                except:
                    pass

    def run_daily_calculation(self):
        """Run the daily calculation for all expansions using multithreading."""
        logger.info("Starting daily expansion value calculation")
        start_time = datetime.now()
        
        try:
            # Get all expansions
            expansions = self.get_all_expansions()
            
            if not expansions:
                logger.warning("No expansions found to process")
                return
            
            # Create a queue for work distribution
            work_queue = queue.Queue()
            
            # Add all expansions to the work queue
            for expansion in expansions:
                work_queue.put(expansion)
            
            # Create shared counters for tracking progress
            processed_count = [0]  # Using list for mutable reference
            error_count = [0]
            results_lock = threading.Lock()
            
            logger.info(f"Processing {len(expansions)} expansions using {NUM_WORKERS} worker threads")
            
            # Create and start worker threads
            threads = []
            for _ in range(NUM_WORKERS):
                thread = threading.Thread(
                    target=self.worker_thread,
                    args=(work_queue, results_lock, processed_count, error_count)
                )
                thread.daemon = True
                thread.start()
                threads.append(thread)
            
            # Process results periodically while workers are running
            batch_size = 50  # Save results in batches
            results_batch = []
            
            # Monitor progress and save results in batches
            last_save_time = time.time()
            last_progress_time = time.time()
            
            while any(t.is_alive() for t in threads) or not self.results_queue.empty():
                try:
                    # Get results without blocking
                    try:
                        while len(results_batch) < batch_size:
                            result = self.results_queue.get(block=False)
                            results_batch.append(result)
                            self.results_queue.task_done()
                    except queue.Empty:
                        pass
                    
                    # Save batch if it's full or if enough time has passed
                    current_time = time.time()
                    if (len(results_batch) >= batch_size or 
                        (len(results_batch) > 0 and current_time - last_save_time > 5)):
                        self.bulk_save_expansion_values(results_batch)
                        results_batch = []
                        last_save_time = current_time
                    
                    # Log progress periodically
                    if current_time - last_progress_time > 10:  # Every 10 seconds
                        with results_lock:
                            logger.info(f"Progress: {processed_count[0]} processed, {error_count[0]} errors")
                        last_progress_time = current_time
                    
                    # Small sleep to prevent CPU spinning
                    time.sleep(0.1)
                    
                except Exception as e:
                    logger.error(f"Error processing results: {e}")
            
            # Save any remaining results
            if results_batch:
                self.bulk_save_expansion_values(results_batch)
            
            # Wait for all threads to complete
            for thread in threads:
                thread.join()
            
            end_time = datetime.now()
            duration = end_time - start_time
            
            logger.info(f"Daily calculation completed:")
            logger.info(f"  - Processed: {processed_count[0]} expansions")
            logger.info(f"  - Errors: {error_count[0]} expansions")
            logger.info(f"  - Duration: {duration}")
            
        except Exception as e:
            logger.error(f"Error in daily calculation: {e}")
            raise

    def get_expansion_value_history(self, game_name, expansion_name, days=30):
        """Get historical expansion values for analysis."""
        try:
            # Find records for this expansion, sorted by date (newest first)
            records = list(self.expansion_values_collection.find({
                'gameName': game_name,
                'expansionName': expansion_name
            }).sort('calculatedAt', -1).limit(days))
            
            return records
            
        except Exception as e:
            logger.error(f"Error getting expansion value history: {e}")
            return []

    def cleanup_old_records(self, days_to_keep=365):
        """Clean up old records to prevent database bloat."""
        try:
            cutoff_date = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
            cutoff_date = cutoff_date - timedelta(days=days_to_keep)
            
            result = self.expansion_values_collection.delete_many({
                'calculatedAt': {'$lt': cutoff_date}
            })
            
            logger.info(f"Cleaned up {result.deleted_count} old records (older than {days_to_keep} days)")
            
        except Exception as e:
            logger.error(f"Error cleaning up old records: {e}")

def main():
    """Main function to run the daily calculation."""
    try:
        start_time = time.time()
        calculator = ExpansionValueCalculator()
        calculator.run_daily_calculation()
        
        # Optional: Clean up old records (keep 1 year of history)
        calculator.cleanup_old_records(days_to_keep=365)
        
        end_time = time.time()
        total_time = end_time - start_time
        logger.info(f"Daily expansion value calculation completed successfully in {total_time:.2f} seconds")
        
    except Exception as e:
        logger.error(f"Daily calculation failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
