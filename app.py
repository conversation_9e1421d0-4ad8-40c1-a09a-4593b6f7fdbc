from flask import Flask, render_template, request, jsonify, redirect, url_for, session
from models import card_model, notification_model, user_model
from config import Config
import json
import os
from datetime import datetime
from bson import ObjectId
from utils import send_welcome_email, send_password_reset_email, send_verification_email

app = Flask(__name__)
app.config.from_object(Config)

# Set secret key for session management
app.secret_key = os.environ.get('SECRET_KEY') or os.urandom(24)

# Custom JSON encoder to handle ObjectId
class JSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, ObjectId):
            return str(obj)
        return super().default(obj)

app.json_encoder = JSONEncoder

# Helper function to convert ObjectId to string in nested documents
def convert_objectid_to_str(obj):
    if isinstance(obj, list):
        return [convert_objectid_to_str(item) for item in obj]
    elif isinstance(obj, dict):
        for key, value in list(obj.items()):
            if isinstance(value, ObjectId):
                obj[key] = str(value)
            elif isinstance(value, (dict, list)):
                obj[key] = convert_objectid_to_str(value)
    return obj

@app.route('/')
def index():
    # Get initial cards for display
    cards = card_model.get_all_cards(limit=20)
    # Get total card count for hero stats
    total_cards = card_model.get_total_card_count()
    return render_template('index.html', cards=cards, total_cards=total_cards)

@app.route('/api/cards')
def get_cards():
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 1000))
    search = request.args.get('search', '')
    
    skip = (page - 1) * limit
    
    if search:
        cards = card_model.search_cards(search, limit)
    else:
        cards = card_model.get_all_cards(limit, skip)
    
    # Convert all ObjectId to string for JSON serialization
    cards = convert_objectid_to_str(cards)
    
    return jsonify(cards)

@app.route('/api/cards/search', methods=['POST'])
def search_cards_regex():
    """Search cards using regex pattern"""
    data = request.get_json()
    
    if not data or 'pattern' not in data:
        return jsonify({'error': 'Search pattern is required'}), 400
    
    pattern = data.get('pattern')
    limit = data.get('limit', 1000)  # Increased default limit to get all results
    
    try:
        # Use the existing search_cards method with the regex pattern
        cards = card_model.search_cards(pattern, limit)
        
        # Convert all ObjectId to string for JSON serialization
        cards = convert_objectid_to_str(cards)
        
        return jsonify({
            'success': True,
            'cards': cards
        })
    except Exception as e:
        print(f"Error searching cards with regex: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/notifications', methods=['POST'])
def create_notification():
    data = request.get_json()

    card_id = data.get('card_id')
    card_name = data.get('card_name')
    target_price = data.get('target_price')
    user_email = data.get('user_email')
    printing = data.get('printing')  # New field
    product_id = data.get('product_id')  # New field

    if not all([card_id, card_name, target_price]):
        return jsonify({'error': 'Missing required fields'}), 400

    try:
        target_price = float(target_price)
        if target_price <= 0:
            return jsonify({'error': 'Target price must be positive'}), 400
    except ValueError:
        return jsonify({'error': 'Invalid target price'}), 400

    # Create notification with printing and product_id
    notification_id = notification_model.create_notification(
        card_id=card_id,
        card_name=card_name,
        target_price=target_price,
        user_email=user_email,
        printing=printing,
        product_id=product_id
    )

    if notification_id:
        return jsonify({
            'success': True,
            'notification_id': notification_id,
            'message': 'Notification created successfully!'
        })
    else:
        return jsonify({'error': 'Failed to create notification'}), 500

@app.route('/api/notifications', methods=['GET'])
def get_notifications():
    user_email = request.args.get('email')
    notifications = notification_model.get_user_notifications(user_email)
    
    # Convert all ObjectId to string for JSON serialization
    notifications = convert_objectid_to_str(notifications)
    
    return jsonify(notifications)

@app.route('/api/notifications/<notification_id>', methods=['PUT'])
def update_notification(notification_id):
    data = request.get_json()

    target_price = data.get('target_price')

    if not target_price:
        return jsonify({'error': 'Target price is required'}), 400

    try:
        target_price = float(target_price)
        if target_price <= 0:
            return jsonify({'error': 'Target price must be positive'}), 400
    except ValueError:
        return jsonify({'error': 'Invalid target price'}), 400

    # Update notification
    success = notification_model.update_notification(notification_id, target_price)
    if success:
        return jsonify({'success': True, 'message': 'Notification updated successfully!'})
    else:
        return jsonify({'error': 'Failed to update notification'}), 500

@app.route('/api/notifications/<notification_id>', methods=['DELETE'])
def delete_notification(notification_id):
    success = notification_model.delete_notification(notification_id)
    if success:
        return jsonify({'success': True, 'message': 'Notification deleted successfully!'})
    else:
        return jsonify({'error': 'Failed to delete notification'}), 500

@app.route('/api/games')
def get_games():
    games = card_model.get_distinct_games()
    return jsonify(games)

@app.route('/api/expansions/<game>')
def get_expansions(game):
    expansions = card_model.get_expansions_by_game(game)
    return jsonify(expansions)

@app.route('/api/cards-by-expansion/<game>/<expansion>')
def get_cards_by_expansion(game, expansion):
    cards = card_model.get_cards_by_expansion(game, expansion)
    # Convert all ObjectId to string for JSON serialization
    cards = convert_objectid_to_str(cards)
    return jsonify(cards)

@app.route('/api/expansion-total-value/<game>/<expansion>')
def get_expansion_total_value(game, expansion):
    """Get the total value of an expansion (pre-calculated or real-time)"""
    try:
        # First, try to get pre-calculated value from today
        today = datetime.now().strftime('%Y-%m-%d')
        expansion_id = f"{game}_{expansion}_{today}"

        # Check if we have a pre-calculated value for today
        expansion_values_collection = card_model.db.db['expansion_values']
        cached_value = expansion_values_collection.find_one({'_id': expansion_id})

        if cached_value:
            print(f"Using cached expansion value for {game} - {expansion}")
            return jsonify({
                'success': True,
                'totalValue': cached_value['totalValue'],
                'cardsWithPrices': cached_value['cardsWithPrices'],
                'cardsWithoutPrices': cached_value['cardsWithoutPrices'],
                'totalCards': cached_value['totalCards'],
                'totalVariants': cached_value['totalVariants'],
                'variantBreakdown': cached_value['variantBreakdown'],
                'cached': True,
                'calculatedAt': cached_value['calculatedAt']
            })

        # If no cached value, fall back to real-time calculation
        print(f"No cached value found, calculating real-time for {game} - {expansion}")

        # Get all cards in the expansion
        cards = card_model.get_cards_by_expansion(game, expansion)

        total_value = 0.0
        cards_with_prices = 0
        cards_without_prices = 0
        total_variants = 0
        variant_breakdown = {}

        for card in cards:
            # Skip sealed products - we only want individual cards for complete set calculation
            if card.get('isSealed', False):
                continue

            if 'productId' in card and card['productId']:
                # Get price variants for this card
                price_variants = card_model.get_current_price(card['productId'])

                if price_variants and len(price_variants) > 0:
                    card_has_prices = False
                    # Add the price of each variant (Normal, Foil, etc.)
                    for variant in price_variants:
                        variant_name = variant['variant']
                        variant_price = variant['lowPrice']
                        total_value += variant_price
                        total_variants += 1
                        card_has_prices = True

                        # Track variant breakdown for details
                        if variant_name not in variant_breakdown:
                            variant_breakdown[variant_name] = {
                                'count': 0,
                                'total_value': 0.0,
                                'highest_card': {'name': '', 'price': 0.0}
                            }
                        variant_breakdown[variant_name]['count'] += 1
                        variant_breakdown[variant_name]['total_value'] += variant_price

                        # Track highest value card for this variant
                        if variant_price > variant_breakdown[variant_name]['highest_card']['price']:
                            variant_breakdown[variant_name]['highest_card'] = {
                                'name': card.get('name', 'Unknown Card'),
                                'price': variant_price
                            }

                    if card_has_prices:
                        cards_with_prices += 1
                    else:
                        cards_without_prices += 1
                else:
                    cards_without_prices += 1
            else:
                cards_without_prices += 1

        # Count only non-sealed cards for the total
        non_sealed_cards = [card for card in cards if not card.get('isSealed', False)]

        return jsonify({
            'success': True,
            'totalValue': round(total_value, 2),
            'cardsWithPrices': cards_with_prices,
            'cardsWithoutPrices': cards_without_prices,
            'totalCards': len(non_sealed_cards),
            'totalVariants': total_variants,
            'variantBreakdown': variant_breakdown,
            'cached': False
        })

    except Exception as e:
        print(f"Error getting expansion total value: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/expansion-value-history/<game>/<expansion>')
def get_expansion_value_history(game, expansion):
    """Get historical expansion values for trend analysis"""
    try:
        days = int(request.args.get('days', 30))  # Default to 30 days

        # Access the expansion_values collection
        expansion_values_collection = card_model.db.db['expansion_values']

        # Find records for this expansion, sorted by date (newest first)
        records = list(expansion_values_collection.find({
            'gameName': game,
            'expansionName': expansion
        }).sort('calculatedAt', -1).limit(days))

        # Convert ObjectId to string for JSON serialization
        records = convert_objectid_to_str(records)

        return jsonify({
            'success': True,
            'history': records,
            'count': len(records)
        })

    except Exception as e:
        print(f"Error getting expansion value history: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/card/<card_id>')
def get_card_details(card_id):
    try:
        card = card_model.get_card_by_id(card_id)
        if card:
            # Print card data for debugging
            print(f"DEBUG: Card data: {card.keys()}")
            
            # Remove any price-related fields from catalog record to avoid confusion
            # We only want prices from the prices collection
            price_fields_to_remove = ['price', 'lowPrice', 'midPrice', 'highPrice', 'marketPrice', 'directLowPrice']
            for field in price_fields_to_remove:
                if field in card:
                    del card[field]

            # Get current price variants from prices collection using productId
            if 'productId' in card:
                print(f"DEBUG: Getting price variants for productId: {card['productId']}")
                price_variants = card_model.get_current_price(card['productId'])
                card['priceVariants'] = price_variants
                print(f"DEBUG: Price variants: {price_variants}")
                
                # Get sales history
                print(f"DEBUG: Getting sales history for productId: {card['productId']}")
                sales_history = card_model.get_sales_history(card['productId'])
                card['salesHistory'] = sales_history
                
                # Get lowest listings - this is where we need to make the API call
                print(f"DEBUG: Getting lowest listings for productId: {card['productId']}")
                lowest_listings = card_model.get_lowest_listings(card['productId'])
                card['lowestListings'] = lowest_listings
                print(f"DEBUG: Lowest listings: {lowest_listings}")
            else:
                print("DEBUG: No productId found in card data")
                card['priceVariants'] = []
                card['salesHistory'] = []
                card['lowestListings'] = []
            
            # Convert all ObjectId to string for JSON serialization
            card = convert_objectid_to_str(card)
            
            return jsonify(card)
        else:
            return jsonify({'error': 'Card not found'}), 404
    except Exception as e:
        print(f"DEBUG: Exception in get_card_details: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/api/cards/batch-prices', methods=['POST'])
def get_batch_prices():
    """Get price data for multiple cards in a single request"""
    try:
        data = request.get_json()
        
        if not data or 'cardIds' not in data or not isinstance(data['cardIds'], list):
            return jsonify({'error': 'Card IDs list is required'}), 400
        
        card_ids = data['cardIds']
        result = {}
        
        # Process cards in parallel using a thread pool
        from concurrent.futures import ThreadPoolExecutor
        
        def get_card_price_data(card_id):
            try:
                card = card_model.get_card_by_id(card_id)
                if card and 'productId' in card:
                    price_variants = card_model.get_current_price(card['productId'])
                    return card_id, price_variants
                return card_id, []
            except Exception as e:
                print(f"Error getting price data for card {card_id}: {e}")
                return card_id, []
        
        # Use a thread pool to fetch price data in parallel
        with ThreadPoolExecutor(max_workers=5) as executor:
            # Submit all tasks and collect futures
            futures = [executor.submit(get_card_price_data, card_id) for card_id in card_ids]
            
            # Process results as they complete
            for future in futures:
                card_id, price_variants = future.result()
                result[card_id] = price_variants
        
        return jsonify(result)
        
    except Exception as e:
        print(f"Error in batch price fetching: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/notifications')
def notifications_page():
    return render_template('notifications.html')

@app.route('/login')
def login_page():
    return render_template('login.html')

@app.route('/register')
def register_page():
    return render_template('register.html')

@app.route('/forgot-password')
def forgot_password_page():
    return render_template('forgot_password.html')

@app.route('/reset-password')
def reset_password_page():
    token = request.args.get('token')
    if not token:
        return redirect(url_for('forgot_password_page'))
    return render_template('reset_password.html', token=token)

@app.route('/dashboard')
def dashboard_page():
    # Check if user is logged in
    if 'user_id' not in session:
        return redirect(url_for('login_page'))
    return render_template('dashboard.html')

@app.route('/set-alert')
def set_alert_page():
    # Check if user is logged in
    if 'user_id' not in session:
        return redirect(url_for('login_page'))
    return render_template('set_alert.html')

# User authentication routes
@app.route('/api/register', methods=['POST'])
def register():
    """Register a new user"""
    data = request.get_json()
    
    # Validate required fields
    required_fields = ['full_name', 'email', 'password']
    for field in required_fields:
        if field not in data or not data[field]:
            return jsonify({'error': f'Missing required field: {field}'}), 400
    
    # Register user
    user_data = user_model.register_user(
        full_name=data['full_name'],
        email=data['email'],
        password=data['password']
    )
    
    if not user_data:
        return jsonify({'error': 'Registration failed. Email may already be in use.'}), 400
    
    # Send verification email
    verification_token = user_data.get('verification_token')
    if verification_token:
        send_verification_email(
            recipient=data['email'],
            username=data['full_name'].split()[0],
            verification_token=verification_token
        )
    
    return jsonify({
        'success': True,
        'message': 'Registration successful! Please check your email to verify your account.',
        'user': {
            'id': user_data.get('_id'),
            'full_name': user_data.get('full_name'),
            'email': user_data.get('email'),
            'is_verified': user_data.get('is_verified', False)
        }
    })

@app.route('/api/verify-email', methods=['GET'])
def verify_email():
    """Verify a user's email address"""
    token = request.args.get('token')
    
    if not token:
        return jsonify({'error': 'Missing verification token'}), 400
    
    success = user_model.verify_email(token)
    
    if success:
        return jsonify({
            'success': True,
            'message': 'Email verified successfully! You can now log in.'
        })
    else:
        return jsonify({
            'error': 'Email verification failed. The token may be invalid or expired.'
        }), 400

@app.route('/api/login', methods=['POST'])
def login():
    """Log in a user"""
    data = request.get_json()
    
    # Validate required fields
    if not data.get('email') or not data.get('password'):
        return jsonify({'error': 'Email and password are required'}), 400
    
    # Authenticate user
    user_data = user_model.login(
        email=data['email'],
        password=data['password']
    )
    
    if not user_data:
        return jsonify({'error': 'Invalid email or password'}), 401
    
    # Check if user is verified
    if isinstance(user_data, dict) and user_data.get('error') == 'email_not_verified':
        return jsonify({
            'error': 'Email not verified',
            'message': 'Please verify your email address before logging in.'
        }), 403
    
    # Store user info in session
    session['user_id'] = user_data.get('_id')
    session['user_email'] = user_data.get('email')
    session['user_name'] = user_data.get('full_name')
    
    return jsonify({
        'success': True,
        'message': 'Login successful!',
        'user': user_data
    })

@app.route('/api/logout', methods=['POST'])
def logout():
    """Log out a user"""
    # Clear session
    session.clear()
    
    return jsonify({
        'success': True,
        'message': 'Logout successful!'
    })

@app.route('/api/user', methods=['GET'])
def get_current_user():
    """Get the current logged-in user"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401
    
    user_data = user_model.get_user_by_id(session['user_id'])
    
    if not user_data:
        # Clear invalid session
        session.clear()
        return jsonify({'error': 'User not found'}), 404
    
    return jsonify({
        'success': True,
        'user': user_data
    })

@app.route('/api/reset-password-request', methods=['POST'])
def reset_password_request():
    """Request a password reset"""
    data = request.get_json()
    
    if not data.get('email'):
        return jsonify({'error': 'Email is required'}), 400
    
    success = user_model.request_password_reset(data['email'])
    
    if success:
        return jsonify({
            'success': True,
            'message': 'Password reset email sent. Please check your inbox.'
        })
    else:
        # Don't reveal if email exists or not for security
        return jsonify({
            'success': True,
            'message': 'If the email exists in our system, a password reset link will be sent.'
        })

@app.route('/api/reset-password', methods=['POST'])
def reset_password():
    """Reset a user's password"""
    data = request.get_json()
    
    if not data.get('token') or not data.get('password'):
        return jsonify({'error': 'Token and new password are required'}), 400
    
    success = user_model.reset_password(data['token'], data['password'])
    
    if success:
        return jsonify({
            'success': True,
            'message': 'Password reset successful! You can now log in with your new password.'
        })
    else:
        return jsonify({
            'error': 'Password reset failed. The token may be invalid or expired.'
        }), 400

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
