#!/usr/bin/env python3
"""
Test script to verify cached expansion values are working correctly.

This script tests a few expansions to ensure the caching system is functioning
and compares cached vs real-time calculation results.
"""

import requests
import json
import time

def test_expansion_value(game, expansion, base_url="http://127.0.0.1:5000"):
    """Test expansion value API endpoint."""
    url = f"{base_url}/api/expansion-total-value/{game}/{expansion}"
    
    print(f"\n🧪 Testing: {game} - {expansion}")
    print(f"URL: {url}")
    
    try:
        start_time = time.time()
        response = requests.get(url)
        end_time = time.time()
        
        response_time = (end_time - start_time) * 1000  # Convert to milliseconds
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('success'):
                cached = data.get('cached', False)
                total_value = data.get('totalValue', 0)
                total_variants = data.get('totalVariants', 0)
                total_cards = data.get('totalCards', 0)
                
                print(f"✅ Success!")
                print(f"   💰 Total Value: ${total_value:,.2f}")
                print(f"   🃏 Cards: {total_cards}")
                print(f"   🔄 Variants: {total_variants}")
                print(f"   ⚡ Response Time: {response_time:.1f}ms")
                print(f"   💾 Cached: {'Yes' if cached else 'No (Real-time)'}")
                
                if 'variantBreakdown' in data:
                    print(f"   📊 Variant Breakdown:")
                    for variant_name, variant_data in data['variantBreakdown'].items():
                        count = variant_data.get('count', 0)
                        value = variant_data.get('total_value', 0)
                        highest = variant_data.get('highest_card', {})
                        highest_name = highest.get('name', 'N/A')
                        highest_price = highest.get('price', 0)
                        
                        print(f"      • {variant_name}: {count} cards, ${value:.2f}")
                        if highest_name != 'N/A':
                            print(f"        Highest: {highest_name} (${highest_price:.2f})")
                
                return True, cached, response_time, total_value
            else:
                print(f"❌ API Error: {data.get('error', 'Unknown error')}")
                return False, False, response_time, 0
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"   Response: {response.text}")
            return False, False, response_time, 0
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False, False, 0, 0

def test_expansion_history(game, expansion, days=7, base_url="http://127.0.0.1:5000"):
    """Test expansion value history API endpoint."""
    url = f"{base_url}/api/expansion-value-history/{game}/{expansion}?days={days}"
    
    print(f"\n📈 Testing History: {game} - {expansion} (last {days} days)")
    print(f"URL: {url}")
    
    try:
        response = requests.get(url)
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('success'):
                history = data.get('history', [])
                count = data.get('count', 0)
                
                print(f"✅ Success!")
                print(f"   📊 Records Found: {count}")
                
                if history:
                    print(f"   📅 Historical Data:")
                    for i, record in enumerate(history[:3]):  # Show first 3 records
                        date = record.get('date', 'Unknown')
                        value = record.get('totalValue', 0)
                        print(f"      {i+1}. {date}: ${value:,.2f}")
                    
                    if len(history) > 3:
                        print(f"      ... and {len(history) - 3} more records")
                else:
                    print(f"   ℹ️  No historical data available yet")
                
                return True, count
            else:
                print(f"❌ API Error: {data.get('error', 'Unknown error')}")
                return False, 0
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False, 0
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False, 0

def main():
    """Run tests on various expansions."""
    print("🚀 Testing TCG Tools Expansion Value Caching System")
    print("=" * 60)
    
    # Test expansions that should have cached values (from our earlier run)
    test_expansions = [
        ("Alpha Clash", "Equilibrium"),
        ("Akora TCG", "Spellbound Wings [1st Edition]"),
        ("Battle Spirits Saga", "Dawn of History"),
        ("Bakugan TCG", "Battle Brawlers"),
    ]
    
    # Test expansions that might not have cached values (popular ones)
    fallback_expansions = [
        ("Pokemon", "Base Set"),
        ("Magic: The Gathering", "Adventures in the Forgotten Realms"),
    ]
    
    results = []
    
    print("\n🧪 Testing Cached Expansions:")
    print("-" * 40)
    
    for game, expansion in test_expansions:
        success, cached, response_time, total_value = test_expansion_value(game, expansion)
        results.append({
            'game': game,
            'expansion': expansion,
            'success': success,
            'cached': cached,
            'response_time': response_time,
            'total_value': total_value
        })
    
    print("\n🔄 Testing Fallback Expansions (may use real-time calculation):")
    print("-" * 60)
    
    for game, expansion in fallback_expansions:
        success, cached, response_time, total_value = test_expansion_value(game, expansion)
        results.append({
            'game': game,
            'expansion': expansion,
            'success': success,
            'cached': cached,
            'response_time': response_time,
            'total_value': total_value
        })
    
    print("\n📈 Testing Historical Data:")
    print("-" * 30)
    
    # Test history for one of the cached expansions
    test_expansion_history("Alpha Clash", "Equilibrium")
    
    print("\n📊 Summary Report:")
    print("=" * 50)
    
    successful_tests = sum(1 for r in results if r['success'])
    cached_responses = sum(1 for r in results if r['cached'])
    avg_response_time = sum(r['response_time'] for r in results if r['success']) / max(successful_tests, 1)
    
    print(f"✅ Successful Tests: {successful_tests}/{len(results)}")
    print(f"💾 Cached Responses: {cached_responses}/{successful_tests}")
    print(f"⚡ Average Response Time: {avg_response_time:.1f}ms")
    
    if cached_responses > 0:
        cached_times = [r['response_time'] for r in results if r['cached']]
        avg_cached_time = sum(cached_times) / len(cached_times)
        print(f"💾 Average Cached Response Time: {avg_cached_time:.1f}ms")
    
    if successful_tests - cached_responses > 0:
        realtime_times = [r['response_time'] for r in results if r['success'] and not r['cached']]
        avg_realtime_time = sum(realtime_times) / len(realtime_times)
        print(f"🔄 Average Real-time Response Time: {avg_realtime_time:.1f}ms")
    
    print(f"\n💰 Total Value Across All Tested Expansions: ${sum(r['total_value'] for r in results if r['success']):,.2f}")
    
    print("\n🎯 Performance Analysis:")
    if cached_responses > 0 and (successful_tests - cached_responses) > 0:
        cached_avg = sum(r['response_time'] for r in results if r['cached']) / cached_responses
        realtime_avg = sum(r['response_time'] for r in results if r['success'] and not r['cached']) / (successful_tests - cached_responses)
        speedup = realtime_avg / cached_avg if cached_avg > 0 else 1
        print(f"   🚀 Caching provides {speedup:.1f}x speed improvement!")
    else:
        print(f"   ℹ️  Need both cached and real-time responses to compare performance")
    
    if successful_tests == len(results):
        print(f"\n🎉 All tests passed! The caching system is working correctly.")
    else:
        failed_tests = len(results) - successful_tests
        print(f"\n⚠️  {failed_tests} test(s) failed. Check the API server and database connection.")

if __name__ == "__main__":
    main()
