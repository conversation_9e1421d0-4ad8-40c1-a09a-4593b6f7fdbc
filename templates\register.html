{% extends "base.html" %}

{% block title %}TCG Alert - Register{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0"><i class="fas fa-user-plus me-2"></i>Create an Account</h3>
                </div>
                <div class="card-body p-4">
                    <div class="alert alert-danger d-none" id="registerError"></div>
                    <div class="alert alert-success d-none" id="registerSuccess"></div>
                    <form id="registerForm">
                        <div class="mb-3">
                            <label for="fullName" class="form-label">Full Name</label>
                            <input type="text" class="form-control" id="fullName" name="fullName" required>
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                            <div class="form-text">We'll never share your email with anyone else.</div>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                            <div class="form-text">Password must be at least 8 characters long.</div>
                        </div>
                        <div class="mb-3">
                            <label for="confirmPassword" class="form-label">Confirm Password</label>
                            <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" required>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="termsAgree" required>
                            <label class="form-check-label" for="termsAgree">
                                I agree to the <a href="#" class="text-decoration-none">Terms of Service</a> and <a href="#" class="text-decoration-none">Privacy Policy</a>
                            </label>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-user-plus me-2"></i>Register
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-footer bg-light text-center">
                    <p class="mb-0">Already have an account? <a href="/login" class="text-decoration-none">Login here</a></p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const registerForm = document.getElementById('registerForm');
        const registerError = document.getElementById('registerError');
        const registerSuccess = document.getElementById('registerSuccess');

        registerForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Clear previous messages
            registerError.classList.add('d-none');
            registerSuccess.classList.add('d-none');
            
            // Get form data
            const fullName = document.getElementById('fullName').value;
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const termsAgree = document.getElementById('termsAgree').checked;
            
            // Validate form
            if (!fullName || !email || !password || !confirmPassword) {
                registerError.textContent = 'Please fill in all fields';
                registerError.classList.remove('d-none');
                return;
            }
            
            if (password.length < 8) {
                registerError.textContent = 'Password must be at least 8 characters long';
                registerError.classList.remove('d-none');
                return;
            }
            
            if (password !== confirmPassword) {
                registerError.textContent = 'Passwords do not match';
                registerError.classList.remove('d-none');
                return;
            }
            
            if (!termsAgree) {
                registerError.textContent = 'You must agree to the Terms of Service and Privacy Policy';
                registerError.classList.remove('d-none');
                return;
            }
            
            // Send registration request
            fetch('/api/register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    full_name: fullName,
                    email: email,
                    password: password
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    registerError.textContent = data.error;
                    registerError.classList.remove('d-none');
                } else {
                    // Show success message
                    registerSuccess.textContent = data.message || 'Registration successful! Please check your email to verify your account.';
                    registerSuccess.classList.remove('d-none');
                    
                    // Clear form
                    registerForm.reset();
                    
                    // Redirect to login page after a delay
                    setTimeout(() => {
                        window.location.href = '/login';
                    }, 5000);
                }
            })
            .catch(error => {
                registerError.textContent = 'An error occurred. Please try again.';
                registerError.classList.remove('d-none');
                console.error('Error:', error);
            });
        });
    });
</script>
{% endblock %}
