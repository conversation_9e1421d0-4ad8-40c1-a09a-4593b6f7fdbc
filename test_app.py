#!/usr/bin/env python3
"""
Test script for TCG Tools application
"""

import requests
import json

BASE_URL = "http://localhost:5000"

def test_api_endpoints():
    """Test all API endpoints"""
    print("Testing TCG Tools API endpoints...")
    
    # Test 1: Get cards
    print("\n1. Testing GET /api/cards")
    try:
        response = requests.get(f"{BASE_URL}/api/cards?limit=5")
        if response.status_code == 200:
            cards = response.json()
            print(f"✅ Success: Retrieved {len(cards)} cards")
            if cards:
                print(f"   Sample card: {cards[0].get('name', 'Unknown')}")
                return cards[0]  # Return first card for notification test
        else:
            print(f"❌ Failed: Status {response.status_code}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    return None

def test_search_cards():
    """Test card search functionality"""
    print("\n2. Testing card search")
    try:
        response = requests.get(f"{BASE_URL}/api/cards?search=pokemon&limit=3")
        if response.status_code == 200:
            cards = response.json()
            print(f"✅ Success: Found {len(cards)} cards matching 'pokemon'")
        else:
            print(f"❌ Failed: Status {response.status_code}")
    except Exception as e:
        print(f"❌ Error: {e}")

def test_create_notification(card):
    """Test creating a notification"""
    print("\n3. Testing POST /api/notifications")
    if not card:
        print("❌ Skipped: No card available for testing")
        return None
    
    try:
        notification_data = {
            "card_id": card.get("_id"),
            "card_name": card.get("name", "Test Card"),
            "target_price": 10.99,
            "user_email": "<EMAIL>"
        }
        
        response = requests.post(
            f"{BASE_URL}/api/notifications",
            json=notification_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print(f"✅ Success: Created notification with ID {result.get('notification_id')}")
                return result.get('notification_id')
            else:
                print(f"❌ Failed: {result.get('error')}")
        else:
            print(f"❌ Failed: Status {response.status_code}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    return None

def test_get_notifications():
    """Test getting notifications"""
    print("\n4. Testing GET /api/notifications")
    try:
        response = requests.get(f"{BASE_URL}/api/notifications")
        if response.status_code == 200:
            notifications = response.json()
            print(f"✅ Success: Retrieved {len(notifications)} notifications")
            return notifications
        else:
            print(f"❌ Failed: Status {response.status_code}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    return []

def test_delete_notification(notification_id):
    """Test deleting a notification"""
    print(f"\n5. Testing DELETE /api/notifications/{notification_id}")
    if not notification_id:
        print("❌ Skipped: No notification ID available")
        return
    
    try:
        response = requests.delete(f"{BASE_URL}/api/notifications/{notification_id}")
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print("✅ Success: Notification deleted")
            else:
                print(f"❌ Failed: {result.get('error')}")
        else:
            print(f"❌ Failed: Status {response.status_code}")
    except Exception as e:
        print(f"❌ Error: {e}")

def test_main_page():
    """Test main page loads"""
    print("\n6. Testing main page")
    try:
        response = requests.get(BASE_URL)
        if response.status_code == 200:
            print("✅ Success: Main page loads correctly")
        else:
            print(f"❌ Failed: Status {response.status_code}")
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Run all tests"""
    print("🃏 TCG Tools - API Test Suite")
    print("=" * 40)
    
    # Test basic functionality
    card = test_api_endpoints()
    test_search_cards()
    
    # Test notification system
    notification_id = test_create_notification(card)
    notifications = test_get_notifications()
    test_delete_notification(notification_id)
    
    # Test web interface
    test_main_page()
    
    print("\n" + "=" * 40)
    print("🎉 Test suite completed!")
    print("\nTo manually test the web interface:")
    print(f"   Open: {BASE_URL}")
    print("   Try searching for cards and setting notifications")

if __name__ == "__main__":
    main()
