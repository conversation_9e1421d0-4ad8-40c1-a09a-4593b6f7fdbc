{% extends "base.html" %}

{% block title %}TCG Alert - Card Price Notifications{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-gradient">
    <div class="container">
        <div class="row align-items-center h-100">
            <div class="col-lg-6">
                <div class="hero-content">
                    <div class="hero-badge mb-3">
                        <span class="badge bg-light text-primary px-3 py-2">
                            <i class="fas fa-sparkles me-2"></i>Smart Price Tracking
                        </span>
                    </div>
                    <h1 class="hero-title">
                        Never Miss a <span class="text-gradient">Great Deal</span> on Trading Cards
                    </h1>
                    <p class="hero-subtitle">
                        Set intelligent price alerts for your favorite TCG cards and get notified instantly when prices drop to your target range.
                    </p>
                    <div class="hero-stats row text-center mb-0">
                        <div class="col-4">
                            <div class="stat-item">
                                <h3 class="stat-number">{{ "{:,}".format(total_cards) if total_cards else "0" }}</h3>
                                <p class="stat-label">Cards Tracked</p>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stat-item">
                                <h3 class="stat-number">24/7</h3>
                                <p class="stat-label">Monitoring</p>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stat-item">
                                <h3 class="stat-number">Instant</h3>
                                <p class="stat-label">Alerts</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="workflow-section">
                    <h3 class="workflow-title mb-4">How It Works</h3>
                    <div class="workflow-steps">
                        <div class="workflow-step">
                            <div class="step-number">1</div>
                            <div class="step-content">
                                <h5 class="step-title">Set Up Notification</h5>
                                <p class="step-description">Choose your card and set your target price for notifications</p>
                            </div>
                        </div>

                        <div class="workflow-step">
                            <div class="step-number">2</div>
                            <div class="step-content">
                                <h5 class="step-title">We Monitor Listings</h5>
                                <p class="step-description">Our system continuously monitors new listings for your selected cards</p>
                            </div>
                        </div>

                        <div class="workflow-step">
                            <div class="step-number">3</div>
                            <div class="step-content">
                                <h5 class="step-title">Get Notified</h5>
                                <p class="step-description">Receive instant alerts when listings match your price criteria</p>
                            </div>
                        </div>

                        <div class="workflow-step">
                            <div class="step-number">4</div>
                            <div class="step-content">
                                <h5 class="step-title">Purchase the Card</h5>
                                <p class="step-description">Use the provided link to quickly purchase the card at your target price</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Key Differentiators Section -->
<div id="why-choose" class="container py-5">
    <div class="row justify-content-center mb-4">
        <div class="col-lg-8 text-center">
            <h2 class="section-title">Why Choose TCG Alert</h2>
            <p class="section-subtitle">Our unique approach to card price tracking</p>
        </div>
    </div>
    
    <div class="row g-4">
        <div class="col-md-4">
            <div class="modern-card h-100 feature-card">
                <div class="card-content text-center">
                    <div class="mb-4">
                        <i class="fas fa-chart-line fa-3x text-primary"></i>
                    </div>
                    <h3 class="h4 mb-3">Live Listings, Not Trends</h3>
                    <p>Unlike others, we use <strong>live listings</strong> instead of trend prices, giving you the most accurate and up-to-date information for your card purchases.</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="modern-card h-100 feature-card">
                <div class="card-content text-center">
                    <div class="mb-4">
                        <i class="fas fa-bolt fa-3x text-primary"></i>
                    </div>
                    <h3 class="h4 mb-3">30-Minute Updates</h3>
                    <p>We receive information <strong>within 30 minutes</strong> of a listing being created, ensuring you're always among the first to know about great deals.</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="modern-card h-100 feature-card">
                <div class="card-content text-center">
                    <div class="mb-4">
                        <i class="fas fa-balance-scale fa-3x text-primary"></i>
                    </div>
                    <h3 class="h4 mb-3">Third-Party Perspective</h3>
                    <p>We <strong>do not get any kickbacks</strong> from TCGPlayer, ensuring a completely independent third-party perspective is always maintained.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container py-5">

    <!-- Search Section -->
    <div id="discover" class="search-section mb-5">
        <div class="text-center mb-4">
            <h2 class="section-title">Discover Your Next Card</h2>
            <p class="section-subtitle">Select your game, expansion, and card to set up price alerts</p>
        </div>
        <div class="row">
            <div class="col-12">
                <div class="search-card">
                    <div class="search-header">
                        <i class="fas fa-filter search-icon"></i>
                        <h5 class="mb-0">Find Cards</h5>
                    </div>
                    <div class="search-body">
                        <div class="dropdown-filters">
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <label for="gameSelect" class="form-label">Select Game</label>
                                    <select class="form-select dropdown-select" id="gameSelect">
                                        <option value="">Choose a game...</option>
                                        <option value="loading" disabled>Loading games...</option>
                                    </select>
                                </div>

                                <div class="col-md-4">
                                    <label for="expansionSelect" class="form-label">Select Expansion</label>
                                    <select class="form-select dropdown-select" id="expansionSelect" disabled>
                                        <option value="">Choose an expansion...</option>
                                    </select>
                                </div>

                                <div class="col-md-4">
                                    <label for="cardSelect" class="form-label">Select Card</label>
                                    <select class="form-select dropdown-select" id="cardSelect" disabled>
                                        <option value="">Choose a card...</option>
                                    </select>
                                </div>
                            </div>

                            <div class="search-actions mt-4">
                                <button class="btn btn-primary btn-lg" id="viewCardBtn" disabled>
                                    <i class="fas fa-eye me-2"></i>View Card Details
                                </button>
                                <button class="btn btn-outline-primary btn-lg ms-3" id="clearFiltersBtn">
                                    <i class="fas fa-times me-2"></i>Clear Filters
                                </button>
                            </div>
                        </div>

                        <!-- Card Details Display -->
                        <div class="card-details-section d-none" id="cardDetailsSection">
                            <div class="card-details-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-info-circle me-2"></i>Card Details
                                </h5>
                            </div>
                            <div class="card-details-content" id="cardDetailsContent">
                                <!-- Card details will be populated here -->
                            </div>
                        </div>

                        <!-- Search by name option has been removed -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Spinner -->
    <div id="loadingSpinner" class="text-center d-none">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-2 text-muted">Loading cards...</p>
    </div>

    <!-- Featured Cards section has been removed -->
</div>

<!-- Pricing Section -->
<div id="pricing" class="container py-5">
    <div class="row justify-content-center mb-4">
        <div class="col-lg-8 text-center">
            <h2 class="section-title">Simple, Transparent Pricing</h2>
            <p class="section-subtitle">Choose the plan that fits your collecting needs</p>
        </div>
    </div>
    
    <div class="row g-4 justify-content-center">
        <!-- Free Tier -->
        <div class="col-lg-4 col-md-6">
            <div class="modern-card h-100 pricing-card">
                <div class="pricing-header text-center">
                    <h3 class="pricing-title">Free</h3>
                    <div class="pricing-value">
                        <span class="price-currency">£</span>
                        <span class="price-amount">0</span>
                        <span class="price-period">/month</span>
                    </div>
                    <p class="pricing-description">Perfect for casual collectors</p>
                </div>
                <div class="pricing-body">
                    <ul class="pricing-features">
                        <li class="feature-item">
                            <i class="fas fa-check feature-icon"></i>
                            <span class="feature-text">Up to 20 notification setups</span>
                        </li>
                        <li class="feature-item">
                            <i class="fas fa-check feature-icon"></i>
                            <span class="feature-text">Manual checking of items</span>
                        </li>
                        <li class="feature-item">
                            <i class="fas fa-check feature-icon"></i>
                            <span class="feature-text">Sales history included</span>
                        </li>
                        <li class="feature-item">
                            <i class="fas fa-check feature-icon"></i>
                            <span class="feature-text">Equal notification priority</span>
                        </li>
                        <li class="feature-item">
                            <i class="fas fa-check feature-icon"></i>
                            <span class="feature-text">Bulk import available</span>
                        </li>
                    </ul>
                </div>
                <div class="pricing-footer text-center">
                    <button class="btn btn-outline-primary btn-lg pricing-btn">Get Started Now</button>
                </div>
            </div>
        </div>
        
        <!-- Standard Tier -->
        <div class="col-lg-4 col-md-6">
            <div class="modern-card h-100 pricing-card featured">
                <div class="featured-badge">Popular</div>
                <div class="pricing-header text-center">
                    <h3 class="pricing-title">Standard</h3>
                    <div class="pricing-value">
                        <span class="price-currency">£</span>
                        <span class="price-amount">9.99</span>
                        <span class="price-period">/month</span>
                    </div>
                    <p class="pricing-description">For active collectors</p>
                </div>
                <div class="pricing-body">
                    <ul class="pricing-features">
                        <li class="feature-item">
                            <i class="fas fa-check feature-icon"></i>
                            <span class="feature-text">Up to 100 notification setups</span>
                        </li>
                        <li class="feature-item">
                            <i class="fas fa-check feature-icon"></i>
                            <span class="feature-text">Manual checking of items</span>
                        </li>
                        <li class="feature-item">
                            <i class="fas fa-check feature-icon"></i>
                            <span class="feature-text">Sales history included</span>
                        </li>
                        <li class="feature-item">
                            <i class="fas fa-check feature-icon"></i>
                            <span class="feature-text">Equal notification priority</span>
                        </li>
                        <li class="feature-item">
                            <i class="fas fa-check feature-icon"></i>
                            <span class="feature-text">Bulk import available</span>
                        </li>
                    </ul>
                </div>
                <div class="pricing-footer text-center">
                    <button class="btn btn-primary btn-lg pricing-btn">Get Started Now</button>
                </div>
            </div>
        </div>
        
        <!-- Premium Tier -->
        <div class="col-lg-4 col-md-6">
            <div class="modern-card h-100 pricing-card">
                <div class="pricing-header text-center">
                    <h3 class="pricing-title">Premium</h3>
                    <div class="pricing-value">
                        <span class="price-currency">£</span>
                        <span class="price-amount">19.99</span>
                        <span class="price-period">/month</span>
                    </div>
                    <p class="pricing-description">For serious collectors</p>
                </div>
                <div class="pricing-body">
                    <ul class="pricing-features">
                        <li class="feature-item">
                            <i class="fas fa-check feature-icon"></i>
                            <span class="feature-text">Unlimited notification setups</span>
                        </li>
                        <li class="feature-item">
                            <i class="fas fa-check feature-icon"></i>
                            <span class="feature-text">Manual checking of items</span>
                        </li>
                        <li class="feature-item">
                            <i class="fas fa-check feature-icon"></i>
                            <span class="feature-text">Sales history included</span>
                        </li>
                        <li class="feature-item">
                            <i class="fas fa-check feature-icon"></i>
                            <span class="feature-text">Equal notification priority</span>
                        </li>
                        <li class="feature-item">
                            <i class="fas fa-check feature-icon"></i>
                            <span class="feature-text">Bulk import available</span>
                        </li>
                    </ul>
                </div>
                <div class="pricing-footer text-center">
                    <button class="btn btn-outline-primary btn-lg pricing-btn">Get Started Now</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Roadmap Section -->
<div id="roadmap" class="container py-5">
    <div class="row justify-content-center mb-4">
        <div class="col-lg-8 text-center">
            <h2 class="section-title">Our Roadmap</h2>
            <p class="section-subtitle">Exciting features coming soon</p>
        </div>
    </div>
    
    <div class="row g-4">
        <div class="col-md-4">
            <div class="modern-card h-100">
                <div class="card-content text-center">
                    <div class="mb-4">
                        <i class="fas fa-store fa-3x text-primary"></i>
                    </div>
                    <h3 class="h4 mb-3">More Marketplaces</h3>
                    <p>We're expanding our coverage to include CardMarket and CardTrader, giving you even more options to find the best deals across multiple platforms.</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="modern-card h-100">
                <div class="card-content text-center">
                    <div class="mb-4">
                        <i class="fas fa-mobile-alt fa-3x text-primary"></i>
                    </div>
                    <h3 class="h4 mb-3">Mobile Apps</h3>
                    <p>Stay connected on the go with our upcoming Android and iOS apps, allowing you to receive alerts and track prices from anywhere.</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="modern-card h-100">
                <div class="card-content text-center">
                    <div class="mb-4">
                        <i class="fas fa-folder fa-3x text-primary"></i>
                    </div>
                    <h3 class="h4 mb-3">Collection Tracker</h3>
                    <p>Manage your entire card collection with our comprehensive tracking system, helping you organize, value, and identify gaps in your collection.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Notification Modal -->
<div class="modal fade" id="notificationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-bell me-2"></i>Set Price Notification
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="notificationForm">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Card:</label>
                        <p id="modalCardName" class="text-muted"></p>
                    </div>
                    
                    <div class="mb-3">
                        <label for="targetPrice" class="form-label">Target Price ($)</label>
                        <input type="number" class="form-control" id="targetPrice" 
                               step="0.01" min="0.01" required>
                        <div class="form-text">You'll be notified when the price drops to or below this amount.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="userEmail" class="form-label">Email (Optional)</label>
                        <input type="email" class="form-control" id="userEmail" 
                               placeholder="<EMAIL>">
                        <div class="form-text">Leave empty for browser notifications only.</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveNotificationBtn">
                    <i class="fas fa-save me-1"></i>Save Notification
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    // Initialize the page
    document.addEventListener('DOMContentLoaded', function() {
        setupEventListeners();
    });
</script>
{% endblock %}
