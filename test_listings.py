import requests
import time
import json

def test_get_lowest_listings(product_id):
    try:
        print(f"DEBUG: Fetching listings for product ID: {product_id}")
        
        # Current timestamp
        timestamp = int(time.time())
        
        # Make API call to fetch listings data
        api_url = f"https://mp-search-api.tcgplayer.com/v1/product/{product_id}/listings?mpfev=3492&_t={timestamp}"
        
        print(f"DEBUG: API URL: {api_url}")
        
        headers = {
            "accept": "application/json, text/plain, */*",
            "content-type": "application/json",
            "origin": "https://www.tcgplayer.com",
            "referer": "https://www.tcgplayer.com/",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36"
        }
        
        payload = {
            "filters": {
                "term": {
                    "sellerStatus": "Live",
                    "channelId": 0,
                    "language": ["English"]
                },
                "range": {
                    "quantity": {
                        "gte": 1
                    }
                },
                "exclude": {
                    "channelExclusion": 0
                }
            },
            "from": 0,
            "size": 50,
            "sort": {
                "field": "price",
                "order": "asc"
            },
            "context": {
                "shippingCountry": "US",
                "cart": {}
            },
            "aggregations": ["listingType"]
        }
        
        print(f"DEBUG: Making API request with headers: {headers}")
        print(f"DEBUG: Request payload: {payload}")
        
        response = requests.post(api_url, headers=headers, json=payload)
        
        print(f"DEBUG: API response status code: {response.status_code}")
        
        if response.status_code != 200:
            print(f"API call failed with status code {response.status_code}: {response.text}")
            return []
            
        data = response.json()
        
        print(f"DEBUG: API response data keys: {data.keys() if data else 'No data'}")
        print(f"DEBUG: Full API response: {json.dumps(data, indent=2)}")
        
        # Extract active listings from the API response
        active_listings = []
        
        if 'results' in data and isinstance(data['results'], list) and len(data['results']) > 0:
            print(f"DEBUG: Found {len(data['results'])} results in API response")
            
            # The actual listings are in the 'results' array of the first item in the 'results' array
            if 'results' in data['results'][0] and isinstance(data['results'][0]['results'], list):
                listings_data = data['results'][0]['results']
                print(f"DEBUG: Found {len(listings_data)} listings in the nested results")
                
                # Process listings from the response
                active_listings = [
                    {
                        'seller': listing.get('sellerName', 'Unknown Seller'),
                        'price': float(listing.get('price', 0)),
                        'shipping': float(listing.get('shippingPrice', 0)),
                        'condition': listing.get('condition', 'Unknown'),
                        'printing': listing.get('printing', 'Normal'),
                        'quantity': int(listing.get('quantity', 1))
                    }
                    for listing in listings_data
                    if listing.get('quantity', 0) > 0
                ]
        
        # Sort by total price (price + shipping)
        active_listings.sort(key=lambda x: x['price'] + x['shipping'])
        
        # Limit to top 10 listings
        active_listings = active_listings[:10]
        
        print(f"DEBUG: Processed {len(active_listings)} active listings")
        print(f"DEBUG: Active listings: {json.dumps(active_listings, indent=2)}")
            
        return active_listings
    except Exception as e:
        print(f"Error getting lowest listings for productId {product_id}: {e}")
        return []

# Test with the specified product ID
if __name__ == "__main__":
    product_id = 623894  # Use the product ID provided by the user
    print(f"Testing get_lowest_listings with product ID: {product_id}")
    listings = test_get_lowest_listings(product_id)
    print(f"Final result: {len(listings)} listings found")
