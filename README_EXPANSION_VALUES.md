# Daily Expansion Value Calculation System

This system pre-calculates and stores expansion values daily to improve performance and provide historical tracking of TCG expansion values.

## Overview

The system consists of:
1. **Daily Calculation Script** (`calculate_expansion_values.py`) - Calculates values for all expansions
2. **API Integration** - Modified endpoints that use cached values when available
3. **Historical Tracking** - Stores daily snapshots for trend analysis
4. **Automation Scripts** - Batch/shell scripts for scheduling

## Features

### ✅ **Complete Set Value Calculation**
- Calculates the cost to collect one of each print type (Normal, Foil, 1st Edition, etc.) for every individual card
- Excludes sealed products (booster boxes, theme decks, booster packs)
- Tracks highest value card per print type
- Provides detailed variant breakdown

### ✅ **Performance Optimization**
- Pre-calculated values served instantly from cache
- Falls back to real-time calculation if no cached data available
- Reduces API response time from seconds to milliseconds

### ✅ **Historical Tracking**
- Daily snapshots stored with timestamps
- Enables trend analysis and value tracking over time
- API endpoint for retrieving historical data

### ✅ **Comprehensive Coverage**
- Processes all 2,553+ unique expansions across all TCG games
- Respects category filters (only includes trading card games)
- Handles all print variants and pricing data

## Files

### Core Scripts
- `calculate_expansion_values.py` - Main calculation script
- `run_daily_calculation.bat` - Windows batch script for automation
- `run_daily_calculation.sh` - Unix/Linux shell script for automation

### Modified Files
- `app.py` - Updated API endpoints to use cached values
- `templates/dashboard.html` - Enhanced UI with filter box and expansion values

## Database Schema

### Collection: `expansion_values`
```javascript
{
  "_id": "GameName_ExpansionName_YYYY-MM-DD",
  "gameName": "Pokemon",
  "expansionName": "Base Set",
  "totalValue": 862.78,
  "cardsWithPrices": 95,
  "cardsWithoutPrices": 6,
  "totalCards": 101,
  "totalVariants": 101,
  "variantBreakdown": {
    "Normal": {
      "count": 86,
      "total_value": 67.03,
      "highest_card": {
        "name": "Item Finder",
        "price": 6.89
      }
    },
    "Holofoil": {
      "count": 15,
      "total_value": 795.75,
      "highest_card": {
        "name": "Charizard",
        "price": 400.00
      }
    }
  },
  "calculatedAt": "2025-06-02T18:12:58.000Z",
  "date": "2025-06-02"
}
```

## API Endpoints

### Get Expansion Total Value
```
GET /api/expansion-total-value/<game>/<expansion>
```

**Response (Cached):**
```json
{
  "success": true,
  "totalValue": 862.78,
  "cardsWithPrices": 95,
  "cardsWithoutPrices": 6,
  "totalCards": 101,
  "totalVariants": 101,
  "variantBreakdown": { ... },
  "cached": true,
  "calculatedAt": "2025-06-02T18:12:58.000Z"
}
```

### Get Expansion Value History
```
GET /api/expansion-value-history/<game>/<expansion>?days=30
```

**Response:**
```json
{
  "success": true,
  "history": [
    {
      "totalValue": 862.78,
      "date": "2025-06-02",
      "calculatedAt": "2025-06-02T18:12:58.000Z",
      ...
    }
  ],
  "count": 30
}
```

## Usage

### Manual Execution
```bash
# Run the calculation script manually
python calculate_expansion_values.py
```

### Automated Scheduling

#### Windows (Task Scheduler)
1. Open Task Scheduler
2. Create Basic Task
3. Set trigger to "Daily" at desired time (e.g., 2:00 AM)
4. Set action to start `run_daily_calculation.bat`

#### Linux/Unix (Cron Job)
```bash
# Edit crontab
crontab -e

# Add daily execution at 2:00 AM
0 2 * * * /path/to/tcgsniper/run_daily_calculation.sh
```

### Example Results

**Pokemon Base Set:**
- Total Value: $862.78
- Cards: 101 individual cards (excludes sealed products)
- Variants: 101 total (Normal + Holofoil)
- Highest Holofoil: Charizard ($400.00)

**Alpha Clash Equilibrium:**
- Total Value: $1,142.05
- Cards: 206 individual cards
- Variants: 329 total (Normal + Foil)
- Highest Foil: Lord Krung, the Barbarous ($68.00)

## Performance Benefits

### Before (Real-time Calculation)
- Response time: 3-10 seconds per expansion
- Database queries: 100+ per expansion
- API calls: Multiple price lookups

### After (Cached Values)
- Response time: <100ms per expansion
- Database queries: 1 per expansion
- API calls: None (uses cached data)

## Monitoring and Logs

### Log File
The script creates `expansion_values.log` with detailed information:
- Calculation progress
- Success/failure status
- Processing times
- Error details

### Example Log Output
```
2025-06-02 18:12:58 - INFO - Starting daily expansion value calculation
2025-06-02 18:12:58 - INFO - Found 2553 unique expansions to process
2025-06-02 18:13:01 - INFO - Calculated Pokemon - Base Set: $862.78 (101 variants)
2025-06-02 18:13:01 - INFO - Inserted new value record for Pokemon - Base Set
```

## Maintenance

### Data Cleanup
The script automatically cleans up old records (keeps 1 year of history by default).

### Error Handling
- Graceful handling of missing price data
- Continues processing if individual expansions fail
- Detailed error logging for troubleshooting

### Configuration
Modify these variables in `calculate_expansion_values.py`:
- `days_to_keep`: How long to retain historical data (default: 365 days)
- `allowed_categories`: Which card categories to include

## Troubleshooting

### Common Issues
1. **MongoDB Connection Error**: Check database credentials in `config.py`
2. **Memory Issues**: Large datasets may require increased memory allocation
3. **Timeout Errors**: Network issues during price data fetching

### Recovery
If the script fails partway through:
1. Check the log file for specific errors
2. Re-run the script (it will skip already processed expansions for the day)
3. Use `cleanup_old_records()` if database becomes too large

## Future Enhancements

### Planned Features
- **Incremental Updates**: Only recalculate expansions with price changes
- **Parallel Processing**: Multi-threaded calculation for faster execution
- **Alert System**: Notifications for significant value changes
- **API Rate Limiting**: Respect external API limits during calculation
- **Dashboard Analytics**: Visual charts showing value trends over time

This system provides a robust foundation for tracking TCG expansion values with excellent performance and comprehensive historical data.
