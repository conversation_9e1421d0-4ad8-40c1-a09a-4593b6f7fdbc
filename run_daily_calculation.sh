#!/bin/bash

# Daily Expansion Value Calculation Script
# This shell script runs the Python script to calculate expansion values
# Schedule this to run daily via cron job

echo "Starting daily expansion value calculation..."
echo "Date: $(date)"

# Change to the script directory
cd "$(dirname "$0")"

# Run the Python script
python3 calculate_expansion_values.py

# Check if the script ran successfully
if [ $? -eq 0 ]; then
    echo "Daily calculation completed successfully at $(date)"
else
    echo "Daily calculation failed with error code $? at $(date)"
    exit 1
fi
