from pymongo import MongoClient
from config import Config
import logging
from datetime import datetime, timedelta
import hashlib
import secrets
import string
from utils import send_welcome_email

class Database:
    def __init__(self):
        self.client = None
        self.db = None
        self.connect()
    
    def connect(self):
        try:
            self.client = MongoClient(Config.MONGO_URI)
            self.db = self.client[Config.MONGO_DB]
            # Test the connection
            self.client.admin.command('ping')
            print("Successfully connected to MongoDB!")
        except Exception as e:
            print(f"Error connecting to MongoDB: {e}")
            logging.error(f"MongoDB connection error: {e}")
    
    def get_catalog_collection(self):
        return self.db.catalog
    
    def get_notifications_collection(self):
        return self.db.notifications

    def get_prices_collection(self):
        return self.db.prices
        
    def get_users_collection(self):
        return self.db.alertUser

class CardModel:
    def __init__(self, db):
        self.db = db
        self.catalog = db.get_catalog_collection()
        # Define the allowed categoryId values for trading card games
        self.allowed_categories = [1, 2, 3, 13, 16, 17, 19, 20, 21, 23, 24, 25, 26, 27, 28, 36, 37, 38, 47, 48, 53, 54, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81]

    def get_category_filter(self):
        return {"categoryId": {"$in": self.allowed_categories}}

    def get_all_cards(self, limit=1000, skip=0):
        try:
            filter_query = self.get_category_filter()
            cards = list(self.catalog.find(filter_query).limit(limit).skip(skip))
            return cards
        except Exception as e:
            print(f"Error fetching cards: {e}")
            return []
    
    def search_cards(self, query, limit=1000):
        try:
            # Combine category filter with search criteria
            category_filter = self.get_category_filter()
            search_filter = {
                "$and": [
                    category_filter,
                    {
                        "$or": [
                            {"name": {"$regex": query, "$options": "i"}},
                            {"set": {"$regex": query, "$options": "i"}},
                            {"type": {"$regex": query, "$options": "i"}}
                        ]
                    }
                ]
            }
            cards = list(self.catalog.find(search_filter).limit(limit))
            return cards
        except Exception as e:
            print(f"Error searching cards: {e}")
            return []
    
    def get_card_by_id(self, card_id):
        try:
            from bson import ObjectId
            category_filter = self.get_category_filter()

            if ObjectId.is_valid(card_id):
                query = {"$and": [{"_id": ObjectId(card_id)}, category_filter]}
            else:
                query = {"$and": [{"_id": card_id}, category_filter]}

            return self.catalog.find_one(query)
        except Exception as e:
            print(f"Error fetching card by ID: {e}")
            return None

    def get_total_card_count(self):
        try:
            filter_query = self.get_category_filter()
            count = self.catalog.count_documents(filter_query)
            return count
        except Exception as e:
            print(f"Error getting card count: {e}")
            return 0

    def get_distinct_games(self):
        try:
            filter_query = self.get_category_filter()
            # Get distinct game names from the catalog using gameName field
            games = self.catalog.distinct("gameName", filter_query)
            # Filter out None/empty values and sort
            games = [game for game in games if game]
            return sorted(games)
        except Exception as e:
            print(f"Error getting distinct games: {e}")
            return []

    def get_expansions_by_game(self, game):
        try:
            category_filter = self.get_category_filter()
            filter_query = {
                "$and": [
                    category_filter,
                    {"gameName": game}
                ]
            }
            # Get distinct expansion names for the specific game using expansionName field
            expansions = self.catalog.distinct("expansionName", filter_query)
            # Filter out None/empty values and sort
            expansions = [exp for exp in expansions if exp]
            return sorted(expansions)
        except Exception as e:
            print(f"Error getting expansions for game {game}: {e}")
            return []

    def get_cards_by_expansion(self, game, expansion, limit=1000):
        try:
            category_filter = self.get_category_filter()
            filter_query = {
                "$and": [
                    category_filter,
                    {"gameName": game},
                    {"expansionName": expansion}
                ]
            }
            cards = list(self.catalog.find(filter_query).limit(limit))
            return cards
        except Exception as e:
            print(f"Error getting cards for game {game}, expansion {expansion}: {e}")
            return []

    def get_current_price(self, product_id):
        try:
            # Get the prices collection
            prices_collection = self.db.get_prices_collection()
            # Find price record by productId as integer
            price_record = prices_collection.find_one({"productId": int(product_id)})

            if price_record and 'prices' in price_record:
                variants_with_prices = []

                # Check each variant for pricing data
                for variant_name, variant_data in price_record['prices'].items():
                    if (variant_data and
                        isinstance(variant_data, dict) and
                        'lowPrice' in variant_data and
                        variant_data['lowPrice'] is not None):

                        variants_with_prices.append({
                            'variant': variant_name,
                            'lowPrice': float(variant_data['lowPrice'])
                        })

                return variants_with_prices

            return []
        except Exception as e:
            print(f"Error getting price for productId {product_id}: {e}")
            return []
            
    def get_sales_history(self, product_id):
        try:
            # Get the live_listings collection
            live_listings_collection = self.db.db.live_listings
            
            # Find listings record by productId
            listings_record = live_listings_collection.find_one({"productId": int(product_id)})
            
            if not listings_record:
                return []
                
            # Extract sales history from the listings data
            sales_history = []
            
            if 'listings' in listings_record and isinstance(listings_record['listings'], list):
                # Filter completed sales from listings
                for listing in listings_record['listings']:
                    if listing.get('listingType') == 'completed':
                        sales_history.append({
                            'date': listing.get('completedAt', ''),
                            'price': float(listing.get('price', 0)),
                            'condition': listing.get('condition', 'Unknown'),
                            'printing': listing.get('printing', 'Normal')
                        })
                
                # Sort by date, most recent first
                sales_history.sort(key=lambda x: x['date'], reverse=True)
                
            return sales_history
        except Exception as e:
            print(f"Error getting sales history for productId {product_id}: {e}")
            return []
    
    def get_lowest_listings(self, product_id):
        try:
            import requests
            import time
            
            print(f"DEBUG: Fetching listings for product ID: {product_id}")
            
            # Current timestamp
            timestamp = int(time.time())
            
            # Make API call to fetch listings data
            api_url = f"https://mp-search-api.tcgplayer.com/v1/product/{product_id}/listings?mpfev=3492&_t={timestamp}"
            
            print(f"DEBUG: API URL: {api_url}")
            
            headers = {
                "accept": "application/json, text/plain, */*",
                "content-type": "application/json",
                "origin": "https://www.tcgplayer.com",
                "referer": "https://www.tcgplayer.com/",
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36"
            }
            
            # Fetch listings in batches
            active_listings = []
            page_size = 50  # Smaller page size to avoid 400 errors
            offset = 0
            max_pages = 20  # Increased limit to ensure we get all listings (up to 1000)
            
            for page in range(max_pages):
                # Prepare payload with current offset
                payload = {
                    "filters": {
                        "term": {
                            "sellerStatus": "Live",
                            "channelId": 0,
                            "language": ["English"]
                        },
                        "range": {
                            "quantity": {
                                "gte": 1
                            }
                        },
                        "exclude": {
                            "channelExclusion": 0
                        }
                    },
                    "from": offset,
                    "size": page_size,
                    "sort": {
                        "field": "price",
                        "order": "asc"
                    },
                    "context": {
                        "shippingCountry": "US",
                        "cart": {}
                    },
                    "aggregations": ["listingType"]
                }
                
                print(f"DEBUG: Making API request for page {page+1} with offset {offset}")
                
                response = requests.post(api_url, headers=headers, json=payload)
                
                print(f"DEBUG: API response status code: {response.status_code}")
                
                if response.status_code != 200:
                    print(f"API call failed with status code {response.status_code}: {response.text}")
                    break
                    
                data = response.json()
                
                if not ('results' in data and 
                       isinstance(data['results'], list) and 
                       len(data['results']) > 0 and
                       'results' in data['results'][0] and 
                       isinstance(data['results'][0]['results'], list)):
                    print(f"DEBUG: No valid listings data found in page {page+1}")
                    break
                
                listings_data = data['results'][0]['results']
                print(f"DEBUG: Found {len(listings_data)} listings in page {page+1}")
                
                # If no listings returned, we've reached the end
                if len(listings_data) == 0:
                    break
                
                # Process listings from the response
                for listing in listings_data:
                    if listing.get('quantity', 0) > 0:
                        active_listings.append({
                            'seller': listing.get('sellerName', 'Unknown Seller'),
                            'price': float(listing.get('price', 0)),
                            'shipping': float(listing.get('shippingPrice', 0)),
                            'condition': listing.get('condition', 'Unknown'),
                            'printing': listing.get('printing', 'Normal'),
                            'quantity': int(listing.get('quantity', 1))
                        })
                
                # Update offset for next page
                offset += page_size
                
                # Check if we've fetched all listings
                total_results = data['results'][0].get('total', 0)
                if offset >= total_results:
                    print(f"DEBUG: Reached end of listings (total: {total_results})")
                    break
            
            print(f"DEBUG: Processed {len(active_listings)} total active listings")
            
            # Group listings by printing type
            listings_by_printing = {}
            for listing in active_listings:
                printing = listing['printing']
                if printing not in listings_by_printing:
                    listings_by_printing[printing] = []
                listings_by_printing[printing].append(listing)
            
            # Sort each group by total price (price + shipping) and take top 10 from each printing type
            final_listings = []
            for printing, listings in listings_by_printing.items():
                # Sort by total price (price + shipping)
                listings.sort(key=lambda x: x['price'] + x['shipping'])
                # Take the top 10 lowest priced listings for this printing type
                top_listings = listings[:10]
                # Add these to our final list
                final_listings.extend(top_listings)
                print(f"DEBUG: For printing '{printing}', found {len(listings)} listings, taking top 10 with lowest prices")
            
            print(f"DEBUG: Returning {len(final_listings)} listings across {len(listings_by_printing)} printing types")
            return final_listings
        except Exception as e:
            print(f"Error getting lowest listings for productId {product_id}: {e}")
            return []

from utils import send_notification_email

class NotificationModel:
    def __init__(self, db):
        self.db = db
        self.notifications = db.get_notifications_collection()
    
    def create_notification(self, card_id, card_name, target_price, user_email=None, printing=None, product_id=None):
        try:
            notification = {
                "card_id": card_id,
                "card_name": card_name,
                "target_price": float(target_price),
                "user_email": user_email,
                "printing": printing or "Normal",  # Default to "Normal" if not provided
                "product_id": product_id,  # Store productId for easier price checking
                "created_at": datetime.utcnow(),
                "is_active": True,
                "triggered": False
            }
            result = self.notifications.insert_one(notification)

            # Note: No immediate email sent - notifications are only sent when target price is actually reached
            # The check_and_send_notifications() method handles sending emails when conditions are met

            return str(result.inserted_id)
        except Exception as e:
            print(f"Error creating notification: {e}")
            return None
    
    def get_user_notifications(self, user_email=None):
        try:
            filter_query = {"is_active": True}
            if user_email:
                filter_query["user_email"] = user_email
            
            notifications = list(self.notifications.find(filter_query))
            return notifications
        except Exception as e:
            print(f"Error fetching notifications: {e}")
            return []
    
    def update_notification(self, notification_id, target_price):
        try:
            from bson import ObjectId
            result = self.notifications.update_one(
                {"_id": ObjectId(notification_id)},
                {
                    "$set": {
                        "target_price": float(target_price),
                        "triggered": False,  # Reset triggered status when price is updated
                        "updated_at": datetime.utcnow()
                    }
                }
            )
            return result.modified_count > 0
        except Exception as e:
            print(f"Error updating notification: {e}")
            return False

    def delete_notification(self, notification_id):
        try:
            from bson import ObjectId
            result = self.notifications.delete_one({"_id": ObjectId(notification_id)})
            return result.deleted_count > 0
        except Exception as e:
            print(f"Error deleting notification: {e}")
            return False
            
    def check_and_send_notifications(self):
        """
        Check for notifications that should be triggered based on current prices
        and send emails for matching notifications
        """
        try:
            # Get all active notifications that haven't been triggered yet
            active_notifications = list(self.notifications.find({
                "is_active": True,
                "triggered": False
            }))
            
            if not active_notifications:
                print("No active notifications to check")
                return 0
                
            print(f"Checking {len(active_notifications)} active notifications")
            
            # Get card model to check prices
            from models import card_model
            
            notifications_sent = 0
            
            for notification in active_notifications:
                card_id = notification.get("card_id")
                target_price = notification.get("target_price")
                user_email = notification.get("user_email")
                card_name = notification.get("card_name")
                
                if not all([card_id, target_price, user_email, card_name]):
                    print(f"Skipping notification {notification.get('_id')} due to missing data")
                    continue
                
                # Get card details to check current price
                card = card_model.get_card_by_id(card_id)
                
                if not card or "productId" not in card:
                    print(f"Card not found for notification {notification.get('_id')}")
                    continue
                    
                # Get current lowest price
                price_variants = card_model.get_current_price(card["productId"])
                
                if not price_variants:
                    print(f"No price data for card {card_id}")
                    continue
                
                # Find the lowest price across all variants
                lowest_price = min([v.get("lowPrice", float('inf')) for v in price_variants])
                
                # Check if the current price is at or below the target price
                if lowest_price <= target_price:
                    print(f"Price alert triggered for {card_name}: current ${lowest_price} <= target ${target_price}")
                    
                    # Create a listing URL
                    listing_url = f"https://tcgsync.com/cards/{card_id}"
                    
                    # Send email notification
                    email_sent = send_notification_email(
                        recipient=user_email,
                        card_name=card_name,
                        price=lowest_price,
                        listing_url=listing_url
                    )
                    
                    if email_sent:
                        # Mark notification as triggered
                        self.notifications.update_one(
                            {"_id": notification.get("_id")},
                            {"$set": {
                                "triggered": True,
                                "triggered_at": datetime.utcnow(),
                                "triggered_price": lowest_price
                            }}
                        )
                        notifications_sent += 1
            
            return notifications_sent
                
        except Exception as e:
            print(f"Error checking notifications: {e}")
            return 0

class AlertUserModel:
    def __init__(self, db):
        self.db = db
        self.users = db.get_users_collection()
    
    def _hash_password(self, password):
        """Hash a password using SHA-256"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def _generate_verification_token(self):
        """Generate a random verification token"""
        return ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(64))
    
    def _generate_reset_token(self):
        """Generate a random password reset token"""
        return ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(32))
    
    def register_user(self, full_name, email, password):
        """
        Register a new user
        
        Args:
            full_name (str): User's full name
            email (str): User's email address
            password (str): User's password
            
        Returns:
            dict: User data if registration successful, None otherwise
        """
        try:
            # Check if user already exists
            if self.users.find_one({"email": email.lower()}):
                print(f"User with email {email} already exists")
                return None
            
            # Generate verification token
            verification_token = self._generate_verification_token()
            
            # Create user document
            user = {
                "full_name": full_name,
                "email": email.lower(),
                "password_hash": self._hash_password(password),
                "created_at": datetime.utcnow(),
                "is_verified": False,
                "verification_token": verification_token,
                "verification_sent_at": datetime.utcnow(),
                "subscription_tier": "free",  # All users start on free subscription
                "last_login": None
            }
            
            # Insert user into database
            result = self.users.insert_one(user)
            
            if result.inserted_id:
                # Send welcome email with verification link
                send_welcome_email(email, full_name.split()[0])
                
                # Return user data (without password hash)
                user_data = user.copy()
                del user_data["password_hash"]
                user_data["_id"] = str(result.inserted_id)
                return user_data
            else:
                return None
                
        except Exception as e:
            print(f"Error registering user: {e}")
            return None
    
    def verify_email(self, token):
        """
        Verify a user's email address using the verification token
        
        Args:
            token (str): Verification token
            
        Returns:
            bool: True if verification successful, False otherwise
        """
        try:
            # Find user with the given verification token
            user = self.users.find_one({"verification_token": token})
            
            if not user:
                print("Invalid verification token")
                return False
            
            # Check if token is expired (24 hours)
            sent_at = user.get("verification_sent_at")
            if not sent_at or (datetime.utcnow() - sent_at) > timedelta(hours=24):
                print("Verification token expired")
                return False
            
            # Update user as verified
            result = self.users.update_one(
                {"_id": user["_id"]},
                {
                    "$set": {
                        "is_verified": True,
                        "verified_at": datetime.utcnow()
                    },
                    "$unset": {
                        "verification_token": "",
                        "verification_sent_at": ""
                    }
                }
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            print(f"Error verifying email: {e}")
            return False
    
    def login(self, email, password):
        """
        Authenticate a user
        
        Args:
            email (str): User's email address
            password (str): User's password
            
        Returns:
            dict: User data if authentication successful, None otherwise
        """
        try:
            # Find user by email
            user = self.users.find_one({"email": email.lower()})
            
            if not user:
                print(f"User with email {email} not found")
                return None
            
            # Check if user is verified
            if not user.get("is_verified", False):
                print(f"User {email} is not verified")
                return {"error": "email_not_verified"}
            
            # Check password
            if user["password_hash"] != self._hash_password(password):
                print(f"Invalid password for user {email}")
                return None
            
            # Update last login time
            self.users.update_one(
                {"_id": user["_id"]},
                {"$set": {"last_login": datetime.utcnow()}}
            )
            
            # Return user data (without password hash)
            user_data = {
                "_id": str(user["_id"]),
                "full_name": user["full_name"],
                "email": user["email"],
                "is_verified": user["is_verified"],
                "subscription_tier": user.get("subscription_tier", "free"),
                "created_at": user["created_at"],
                "last_login": datetime.utcnow()
            }
            
            return user_data
            
        except Exception as e:
            print(f"Error logging in: {e}")
            return None
    
    def request_password_reset(self, email):
        """
        Request a password reset for a user
        
        Args:
            email (str): User's email address
            
        Returns:
            bool: True if request successful, False otherwise
        """
        try:
            # Find user by email
            user = self.users.find_one({"email": email.lower()})
            
            if not user:
                print(f"User with email {email} not found")
                return False
            
            # Generate reset token
            reset_token = self._generate_reset_token()
            
            # Update user with reset token
            result = self.users.update_one(
                {"_id": user["_id"]},
                {
                    "$set": {
                        "reset_token": reset_token,
                        "reset_token_expires": datetime.utcnow() + timedelta(hours=1)
                    }
                }
            )
            
            if result.modified_count > 0:
                # Send password reset email
                from utils import send_password_reset_email
                send_password_reset_email(email, reset_token)
                return True
            else:
                return False
                
        except Exception as e:
            print(f"Error requesting password reset: {e}")
            return False
    
    def reset_password(self, token, new_password):
        """
        Reset a user's password using a reset token
        
        Args:
            token (str): Reset token
            new_password (str): New password
            
        Returns:
            bool: True if reset successful, False otherwise
        """
        try:
            # Find user with the given reset token
            user = self.users.find_one({
                "reset_token": token,
                "reset_token_expires": {"$gt": datetime.utcnow()}
            })
            
            if not user:
                print("Invalid or expired reset token")
                return False
            
            # Update user's password
            result = self.users.update_one(
                {"_id": user["_id"]},
                {
                    "$set": {
                        "password_hash": self._hash_password(new_password),
                        "password_updated_at": datetime.utcnow()
                    },
                    "$unset": {
                        "reset_token": "",
                        "reset_token_expires": ""
                    }
                }
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            print(f"Error resetting password: {e}")
            return False
    
    def update_subscription(self, user_id, subscription_tier):
        """
        Update a user's subscription tier
        
        Args:
            user_id (str): User ID
            subscription_tier (str): New subscription tier (free, standard, premium)
            
        Returns:
            bool: True if update successful, False otherwise
        """
        try:
            from bson import ObjectId
            
            # Validate subscription tier
            valid_tiers = ["free", "standard", "premium"]
            if subscription_tier not in valid_tiers:
                print(f"Invalid subscription tier: {subscription_tier}")
                return False
            
            # Update user's subscription tier
            result = self.users.update_one(
                {"_id": ObjectId(user_id)},
                {
                    "$set": {
                        "subscription_tier": subscription_tier,
                        "subscription_updated_at": datetime.utcnow()
                    }
                }
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            print(f"Error updating subscription: {e}")
            return False
    
    def get_user_by_id(self, user_id):
        """
        Get a user by ID
        
        Args:
            user_id (str): User ID
            
        Returns:
            dict: User data if found, None otherwise
        """
        try:
            from bson import ObjectId
            
            # Find user by ID
            user = self.users.find_one({"_id": ObjectId(user_id)})
            
            if not user:
                return None
            
            # Return user data (without password hash)
            user_data = {
                "_id": str(user["_id"]),
                "full_name": user["full_name"],
                "email": user["email"],
                "is_verified": user.get("is_verified", False),
                "subscription_tier": user.get("subscription_tier", "free"),
                "created_at": user["created_at"],
                "last_login": user.get("last_login")
            }
            
            return user_data
            
        except Exception as e:
            print(f"Error getting user by ID: {e}")
            return None

# Initialize database connection
db = Database()
card_model = CardModel(db)
notification_model = NotificationModel(db)
user_model = AlertUserModel(db)
