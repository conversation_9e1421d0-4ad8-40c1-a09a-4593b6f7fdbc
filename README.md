# TCG Tools - Trading Card Game Price Notification System

A sleek and modern web application that allows users to browse trading cards and set price notifications for their favorite cards.

## Features

- **Card Browsing**: Browse and search through trading cards from your MongoDB catalog
- **Price Notifications**: Set target prices and get notified when cards reach your desired price
- **Modern UI**: Sleek, responsive design with Bootstrap 5
- **Email Notifications**: Optional email notifications for price alerts
- **Real-time Search**: Search cards by name, set, or type

## Technology Stack

- **Backend**: Flask (Python)
- **Database**: MongoDB
- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap 5
- **Icons**: Font Awesome

## Installation

1. Install Python dependencies:
```bash
pip install -r requirements.txt
```

2. Configure your MongoDB connection in `config.py` (already configured for your database)

3. Run the application:
```bash
python app.py
```

4. Open your browser and navigate to `http://localhost:5000`

## Database Configuration

The application is configured to connect to:
- **Host**: *************:27017
- **Database**: test
- **Collection**: catalog
- **Authentication**: admin user with provided credentials

## Usage

1. **Browse Cards**: View cards from your catalog collection
2. **Search**: Use the search bar to find specific cards
3. **Set Notifications**: Click "Set Notification" on any card to create a price alert
4. **Manage Notifications**: View and manage your notifications on the notifications page

## API Endpoints

- `GET /` - Main page with card browsing
- `GET /api/cards` - Get cards with pagination and search
- `GET /api/card/<id>` - Get specific card details
- `POST /api/notifications` - Create a new price notification
- `GET /api/notifications` - Get user notifications
- `DELETE /api/notifications/<id>` - Delete a notification

## Project Structure

```
tcgsniper/
├── app.py              # Main Flask application
├── models.py           # Database models and connections
├── config.py           # Configuration settings
├── requirements.txt    # Python dependencies
├── templates/          # HTML templates
│   ├── base.html      # Base template
│   ├── index.html     # Main page
│   └── notifications.html # Notifications page
└── static/            # Static assets
    ├── css/
    │   └── style.css  # Custom styles
    └── js/
        └── main.js    # Frontend JavaScript
```

## Sponsored by

This project is sponsored by [tcgapis.com](https://tcgapis.com)

## License

All trademarks are property of their respective owners.
