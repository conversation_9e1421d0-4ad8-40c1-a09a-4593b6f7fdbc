#!/usr/bin/env python3
"""
Test Email Script

This script tests the email functionality by sending a test email.
"""

import sys
import argparse
from utils import send_email, send_notification_email, send_welcome_email, send_password_reset_email

def main():
    """
    Main function to send test emails
    """
    parser = argparse.ArgumentParser(description='Send test emails')
    parser.add_argument('--type', choices=['basic', 'notification', 'welcome', 'reset'], 
                        default='basic', help='Type of email to send')
    parser.add_argument('--recipient', required=True, help='Email recipient')
    parser.add_argument('--card-name', help='Card name (for notification emails)')
    parser.add_argument('--price', type=float, help='Price (for notification emails)')
    parser.add_argument('--username', help='Username (for welcome emails)')
    parser.add_argument('--token', help='Reset token (for password reset emails)')
    
    args = parser.parse_args()
    
    print(f"Sending {args.type} email to {args.recipient}...")
    
    success = False
    
    if args.type == 'basic':
        success = send_email(
            recipient=args.recipient,
            subject="Test Email from TCG Alert",
            html_content="""
            <html>
                <body>
                    <h1>Test Email</h1>
                    <p>This is a test email from TCG Alert.</p>
                    <p>If you received this email, the email functionality is working correctly.</p>
                </body>
            </html>
            """
        )
    elif args.type == 'notification':
        if not args.card_name or not args.price:
            print("Error: card-name and price are required for notification emails")
            return 1
            
        success = send_notification_email(
            recipient=args.recipient,
            card_name=args.card_name,
            price=args.price,
            listing_url="https://tcgsync.com/cards/example"
        )
    elif args.type == 'welcome':
        if not args.username:
            print("Error: username is required for welcome emails")
            return 1
            
        success = send_welcome_email(
            recipient=args.recipient,
            username=args.username
        )
    elif args.type == 'reset':
        if not args.token:
            print("Error: token is required for password reset emails")
            return 1
            
        success = send_password_reset_email(
            recipient=args.recipient,
            reset_token=args.token
        )
    
    if success:
        print("Email sent successfully!")
        return 0
    else:
        print("Failed to send email.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
