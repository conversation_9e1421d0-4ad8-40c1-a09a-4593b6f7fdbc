#!/usr/bin/env python3
"""
View My Alerts Script

This script shows all your active price alerts stored in the database.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models import notification_model
import json
from datetime import datetime

def view_alerts_by_email(email):
    """
    View all alerts for a specific email address
    """
    print(f"🔍 Looking for alerts for: {email}")
    print("=" * 60)
    
    try:
        # Get all notifications for this email
        notifications = notification_model.get_user_notifications(email)
        
        if not notifications:
            print("❌ No alerts found for this email address.")
            print("\nPossible reasons:")
            print("   • No alerts have been created yet")
            print("   • Email address doesn't match exactly")
            print("   • Alerts were deleted")
            return
        
        print(f"✅ Found {len(notifications)} alert(s):")
        print()
        
        for i, alert in enumerate(notifications, 1):
            print(f"📋 Alert #{i}")
            print(f"   Card: {alert.get('card_name', 'Unknown')}")
            print(f"   Printing: {alert.get('printing', 'Unknown')}")
            print(f"   Product ID: {alert.get('product_id', 'Unknown')}")
            print(f"   Target Price: ${alert.get('target_price', 0):.2f}")
            print(f"   Status: {'🟢 Active' if alert.get('is_active') else '🔴 Inactive'}")
            print(f"   Triggered: {'✅ Yes' if alert.get('triggered') else '❌ No'}")
            print(f"   Created: {alert.get('created_at', 'Unknown')}")
            print(f"   Alert ID: {alert.get('_id', 'Unknown')}")

            if alert.get('triggered'):
                print(f"   Triggered At: {alert.get('triggered_at', 'Unknown')}")
                print(f"   Triggered Price: ${alert.get('triggered_price', 0):.2f}")

            print("-" * 40)
        
        # Summary
        active_count = sum(1 for alert in notifications if alert.get('is_active') and not alert.get('triggered'))
        triggered_count = sum(1 for alert in notifications if alert.get('triggered'))
        
        print(f"\n📊 Summary:")
        print(f"   Total Alerts: {len(notifications)}")
        print(f"   Active & Waiting: {active_count}")
        print(f"   Already Triggered: {triggered_count}")
        
    except Exception as e:
        print(f"❌ Error retrieving alerts: {e}")

def view_all_recent_alerts():
    """
    View all recent alerts in the system (for debugging)
    """
    print("🔍 Looking for all recent alerts in the system...")
    print("=" * 60)
    
    try:
        # Get all active notifications
        all_notifications = notification_model.get_user_notifications()
        
        if not all_notifications:
            print("❌ No alerts found in the system.")
            return
        
        print(f"✅ Found {len(all_notifications)} total alert(s) in the system:")
        print()
        
        # Sort by creation date (most recent first)
        sorted_alerts = sorted(all_notifications, 
                             key=lambda x: x.get('created_at', datetime.min), 
                             reverse=True)
        
        # Show last 10 alerts
        recent_alerts = sorted_alerts[:10]
        
        for i, alert in enumerate(recent_alerts, 1):
            print(f"📋 Alert #{i}")
            print(f"   Email: {alert.get('user_email', 'Unknown')}")
            print(f"   Card: {alert.get('card_name', 'Unknown')}")
            print(f"   Printing: {alert.get('printing', 'Unknown')}")
            print(f"   Product ID: {alert.get('product_id', 'Unknown')}")
            print(f"   Target Price: ${alert.get('target_price', 0):.2f}")
            print(f"   Status: {'🟢 Active' if alert.get('is_active') else '🔴 Inactive'}")
            print(f"   Triggered: {'✅ Yes' if alert.get('triggered') else '❌ No'}")
            print(f"   Created: {alert.get('created_at', 'Unknown')}")
            print("-" * 40)
        
        if len(all_notifications) > 10:
            print(f"... and {len(all_notifications) - 10} more alerts")
        
    except Exception as e:
        print(f"❌ Error retrieving alerts: {e}")

def main():
    """
    Main function
    """
    print("🚨 TCG Tools - View My Alerts")
    print("=" * 60)
    
    if len(sys.argv) > 1:
        # Email provided as command line argument
        email = sys.argv[1]
        view_alerts_by_email(email)
    else:
        # Interactive mode
        print("Choose an option:")
        print("1. View alerts for specific email")
        print("2. View all recent alerts in system")
        print()
        
        choice = input("Enter choice (1 or 2): ").strip()
        
        if choice == "1":
            email = input("Enter your email address: ").strip()
            if email:
                print()
                view_alerts_by_email(email)
            else:
                print("❌ No email address provided.")
        elif choice == "2":
            print()
            view_all_recent_alerts()
        else:
            print("❌ Invalid choice.")

if __name__ == "__main__":
    main()
