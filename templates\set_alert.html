{% extends "base.html" %}

{% block title %}Set Price Alert - TCG Tools{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Header with Back Button -->
            <div class="d-flex align-items-center mb-4">
                <button id="backButton" class="btn btn-outline-secondary me-3">
                    <i class="fas fa-arrow-left me-2"></i>Back to Results
                </button>
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-bell text-primary me-2"></i>Set Price Alert
                    </h2>
                    <p class="text-muted mb-0">Get notified when your target prices are available</p>
                </div>
            </div>

            <!-- Card Information -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-2">
                            <img id="cardImage" src="" alt="Card Image" class="img-fluid rounded" style="max-height: 150px;">
                        </div>
                        <div class="col-md-10">
                            <h4 id="cardName" class="mb-2">Loading...</h4>
                            <p class="text-muted mb-1">
                                <strong>Set:</strong> <span id="cardSet">Loading...</span>
                            </p>
                            <p class="text-muted mb-0">
                                <strong>Number:</strong> <span id="cardNumber">Loading...</span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Loading State -->
            <div id="loadingState" class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading current market prices...</span>
                </div>
                <p class="mt-3 text-muted">Loading current market prices...</p>
            </div>

            <!-- Alert Forms -->
            <div id="alertForms" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>Current Market Prices & Alert Settings
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            Set your target price for each print type. You'll receive email notifications when listings at or below your target price become available.
                        </div>
                        
                        <div id="printTypeAlerts">
                            <!-- Print type alert forms will be populated here -->
                        </div>

                        <div class="mt-4 d-flex justify-content-between align-items-center">
                            <div>
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>
                                    Alerts are checked every 30 minutes and you'll receive email notifications when your target prices are met.
                                </small>
                            </div>
                            <div>
                                <button type="button" class="btn btn-secondary me-2" id="cancelButton">
                                    <i class="fas fa-times me-1"></i>Cancel
                                </button>
                                <button type="button" class="btn btn-primary" id="saveAlertsButton">
                                    <i class="fas fa-bell me-1"></i>Save Alerts
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Error State -->
            <div id="errorState" style="display: none;" class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <span id="errorMessage">Unable to load current market prices for this card.</span>
            </div>

            <!-- Success State -->
            <div id="successState" style="display: none;" class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <span id="successMessage">Price alerts saved successfully!</span>
                <div class="mt-2">
                    <button class="btn btn-success btn-sm" id="backToResultsButton">
                        <i class="fas fa-arrow-left me-1"></i>Back to Results
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Get URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const cardId = urlParams.get('cardId');
    const cardName = urlParams.get('cardName');
    const returnUrl = urlParams.get('returnUrl') || '/dashboard';
    
    // Set up back button
    const backButton = document.getElementById('backButton');
    const cancelButton = document.getElementById('cancelButton');
    const backToResultsButton = document.getElementById('backToResultsButton');
    
    function goBack() {
        window.location.href = returnUrl;
    }
    
    backButton.addEventListener('click', goBack);
    cancelButton.addEventListener('click', goBack);
    backToResultsButton.addEventListener('click', goBack);
    
    if (!cardId || !cardName) {
        showError('Missing card information. Please try again.');
        return;
    }
    
    // Set initial card name
    document.getElementById('cardName').textContent = decodeURIComponent(cardName);
    
    // Load card data and current prices
    loadCardData(cardId);
});

function loadCardData(cardId) {
    fetch(`/api/card/${cardId}`)
        .then(response => response.json())
        .then(cardData => {
            if (cardData && cardData.name) {
                // Update card information
                document.getElementById('cardName').textContent = cardData.name;
                document.getElementById('cardSet').textContent = cardData.expansionName || cardData.set_name || 'Unknown Set';
                document.getElementById('cardNumber').textContent = cardData.number || cardData.cardNumber || '-';

                // Set card image
                const cardImage = document.getElementById('cardImage');
                cardImage.src = cardData.imageUrl || cardData.image || 'https://via.placeholder.com/150?text=No+Image';
                cardImage.alt = cardData.name;

                if (cardData.lowestListings && cardData.lowestListings.length > 0) {
                    // Pass the full cardData including productId
                    setupAlertForms(cardData.lowestListings, cardId, cardData.name, cardData.productId);
                } else {
                    showError('No current market listings found for this card. Please try again later.');
                }
            } else {
                showError('Card data not found. Please try again.');
            }
        })
        .catch(error => {
            console.error('Error loading card data:', error);
            showError('Failed to load card data. Please try again.');
        });
}

function setupAlertForms(lowestListings, cardId, cardName, productId) {
    // Group listings by printing type and get the lowest price for each
    const printingGroups = {};
    lowestListings.forEach(listing => {
        const printing = listing.printing || 'Normal';
        if (!printingGroups[printing] || listing.price < printingGroups[printing].price) {
            printingGroups[printing] = listing;
        }
    });

    const printTypeAlerts = document.getElementById('printTypeAlerts');
    printTypeAlerts.innerHTML = '';

    Object.entries(printingGroups).forEach(([printing, listing]) => {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'row mb-4 p-3 border rounded bg-light';
        alertDiv.innerHTML = `
            <div class="col-md-3">
                <h6 class="mb-1 text-primary">${printing}</h6>
                <p class="mb-1">
                    <strong>Current Lowest:</strong><br>
                    <span class="h5 text-success">$${listing.price.toFixed(2)}</span>
                </p>
                <small class="text-muted">Seller: ${listing.seller}</small>
            </div>
            <div class="col-md-4">
                <label class="form-label">Target Price</label>
                <div class="input-group">
                    <span class="input-group-text">$</span>
                    <input type="number"
                           class="form-control target-price-input"
                           data-printing="${printing}"
                           data-current-price="${listing.price}"
                           step="0.01"
                           min="0.01"
                           placeholder="0.00">
                </div>
                <small class="text-muted">Enter your maximum price</small>
            </div>
            <div class="col-md-5">
                <label class="form-label">Quick Set Options</label>
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-primary btn-sm quick-set-btn"
                            data-printing="${printing}"
                            data-percentage="5">
                        5% below current ($${(listing.price * 0.95).toFixed(2)})
                    </button>
                    <button type="button" class="btn btn-outline-primary btn-sm quick-set-btn"
                            data-printing="${printing}"
                            data-percentage="10">
                        10% below current ($${(listing.price * 0.90).toFixed(2)})
                    </button>
                    <button type="button" class="btn btn-outline-primary btn-sm quick-set-btn"
                            data-printing="${printing}"
                            data-percentage="15">
                        15% below current ($${(listing.price * 0.85).toFixed(2)})
                    </button>
                </div>
            </div>
        `;
        printTypeAlerts.appendChild(alertDiv);
    });

    // Set up quick set button handlers
    document.querySelectorAll('.quick-set-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const printing = this.getAttribute('data-printing');
            const percentage = parseInt(this.getAttribute('data-percentage'));
            const currentPrice = parseFloat(printingGroups[printing].price);
            const targetPrice = currentPrice * (1 - percentage / 100);

            const input = document.querySelector(`input[data-printing="${printing}"]`);
            input.value = targetPrice.toFixed(2);

            // Highlight the selected button
            const parentDiv = this.closest('.col-md-5');
            parentDiv.querySelectorAll('.quick-set-btn').forEach(b => {
                b.classList.remove('btn-primary');
                b.classList.add('btn-outline-primary');
            });
            this.classList.remove('btn-outline-primary');
            this.classList.add('btn-primary');
        });
    });

    // Set up save button - pass productId to saveAlerts
    document.getElementById('saveAlertsButton').addEventListener('click', () => saveAlerts(cardId, cardName, productId));

    // Show the forms
    document.getElementById('loadingState').style.display = 'none';
    document.getElementById('alertForms').style.display = 'block';
}

function saveAlerts(cardId, cardName, productId) {
    const targetPriceInputs = document.querySelectorAll('.target-price-input');
    const alerts = [];

    // Collect all target prices that have been set
    targetPriceInputs.forEach(input => {
        const targetPrice = parseFloat(input.value);
        const printing = input.getAttribute('data-printing');

        if (targetPrice && targetPrice > 0) {
            alerts.push({
                cardId: cardId,
                cardName: `${cardName} (${printing})`,
                targetPrice: targetPrice,
                printing: printing,
                productId: productId
            });
        }
    });

    if (alerts.length === 0) {
        alert('Please set at least one target price before saving.');
        return;
    }

    // Get user email from session
    fetch('/api/user')
        .then(response => response.json())
        .then(userData => {
            if (!userData.success || !userData.user || !userData.user.email) {
                alert('Please log in to set price alerts.');
                return;
            }

            const userEmail = userData.user.email;

            // Disable save button and show loading
            const saveBtn = document.getElementById('saveAlertsButton');
            const originalText = saveBtn.innerHTML;
            saveBtn.disabled = true;
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Saving...';

            // Save each alert
            const savePromises = alerts.map(alert => {
                return fetch('/api/notifications', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        card_id: alert.cardId,
                        card_name: alert.cardName,
                        target_price: alert.targetPrice,
                        user_email: userEmail,
                        printing: alert.printing,
                        product_id: alert.productId
                    })
                });
            });
            
            Promise.all(savePromises)
                .then(responses => {
                    // Check if all requests were successful
                    const allSuccessful = responses.every(response => response.ok);
                    
                    if (allSuccessful) {
                        // Show success message
                        const alertsText = alerts.length === 1 ? 'alert' : 'alerts';
                        showSuccess(`Successfully saved ${alerts.length} price ${alertsText}! You'll receive email notifications when your target prices are met.`);
                    } else {
                        alert('Some alerts could not be saved. Please try again.');
                        saveBtn.disabled = false;
                        saveBtn.innerHTML = originalText;
                    }
                })
                .catch(error => {
                    console.error('Error saving alerts:', error);
                    alert('Failed to save alerts. Please try again.');
                    saveBtn.disabled = false;
                    saveBtn.innerHTML = originalText;
                });
        })
        .catch(error => {
            console.error('Error getting user data:', error);
            alert('Please log in to set price alerts.');
        });
}

function showError(message) {
    document.getElementById('loadingState').style.display = 'none';
    document.getElementById('alertForms').style.display = 'none';
    document.getElementById('errorMessage').textContent = message;
    document.getElementById('errorState').style.display = 'block';
}

function showSuccess(message) {
    document.getElementById('alertForms').style.display = 'none';
    document.getElementById('successMessage').textContent = message;
    document.getElementById('successState').style.display = 'block';
}
</script>
{% endblock %}
