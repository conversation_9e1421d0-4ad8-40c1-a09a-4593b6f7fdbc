<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}TCG Alert{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🃏</text></svg>">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    
    {% block extra_head %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="{{ url_for('index') }}">
                <i class="fas fa-cards-blank me-2"></i>TCG Alert
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}#why-choose">
                            <i class="fas fa-check-circle me-1"></i>Why Choose Us
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}#discover">
                            <i class="fas fa-search me-1"></i>Discover Cards
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}#pricing">
                            <i class="fas fa-tag me-1"></i>Pricing
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}#roadmap">
                            <i class="fas fa-map me-1"></i>Roadmap
                        </a>
                    </li>
                    {% if session.get('user_id') %}
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/notifications">
                            <i class="fas fa-bell me-1"></i>Notifications
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle me-1"></i>{{ session.get('user_name', 'User').split(' ')[0] }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                            <li><a class="dropdown-item" href="/dashboard"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="navLogoutBtn"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="/login">
                            <i class="fas fa-sign-in-alt me-1"></i>Login
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/register">
                            <i class="fas fa-user-plus me-1"></i>Register
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        {% block content %}{% endblock %}
    </main>

    <!-- Sleek Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row g-4 mb-3">
                <div class="col-lg-6 order-lg-2">
                    <h5 class="text-white mb-3">Quick Access</h5>
                    <div class="d-flex flex-wrap">
                        <a href="{{ url_for('index') }}" class="badge bg-primary bg-opacity-75 fs-6 me-2 mb-2 text-decoration-none">
                            <i class="fas fa-home me-1"></i> Home
                        </a>
                        <a href="{{ url_for('notifications_page') }}" class="badge bg-primary bg-opacity-75 fs-6 me-2 mb-2 text-decoration-none">
                            <i class="fas fa-bell me-1"></i> Notifications
                        </a>
                        <a href="{{ url_for('index') }}#support" class="badge bg-primary bg-opacity-75 fs-6 me-2 mb-2 text-decoration-none">
                            <i class="fas fa-question-circle me-1"></i> Support
                        </a>
                        {% if session.get('user_id') %}
                        <a href="/dashboard" class="badge bg-primary bg-opacity-75 fs-6 me-2 mb-2 text-decoration-none">
                            <i class="fas fa-tachometer-alt me-1"></i> Dashboard
                        </a>
                        <a href="#" class="badge bg-primary bg-opacity-75 fs-6 me-2 mb-2 text-decoration-none" id="footerLogoutBtn">
                            <i class="fas fa-sign-out-alt me-1"></i> Logout
                        </a>
                        {% else %}
                        <a href="/login" class="badge bg-primary bg-opacity-75 fs-6 me-2 mb-2 text-decoration-none">
                            <i class="fas fa-sign-in-alt me-1"></i> Login
                        </a>
                        <a href="/register" class="badge bg-primary bg-opacity-75 fs-6 me-2 mb-2 text-decoration-none">
                            <i class="fas fa-user-plus me-1"></i> Register
                        </a>
                        {% endif %}
                    </div>
                </div>
                
                <div class="col-lg-6 order-lg-1">
                    <div class="d-flex align-items-center mb-3">
                        <div class="footer-logo me-3">
                            <i class="fas fa-cards-blank fs-2 text-primary"></i>
                        </div>
                        <h5 class="text-white mb-0">TCG Alert</h5>
                    </div>
                    <p class="text-light mb-0 ps-1">
                        Your comprehensive Trading Card Game price tracking and notification system. 
                        Never miss a great deal on your favorite cards again.
                    </p>
                </div>
            </div>
            
            <div class="footer-bottom pt-3 border-top border-secondary">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <p class="mb-1 text-light small">
                            All trademarks are property of their respective owners. 
                            Sponsored by 
                            <a href="https://tcgapis.com" target="_blank" class="text-info text-decoration-none fw-bold">
                                tcgapis.com
                            </a>
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <p class="mb-0 text-light small">
                            © 2024 TCG Alert <span class="mx-1">•</span> <a href="{{ url_for('index') }}#privacy" class="text-light text-decoration-none">Privacy</a> <span class="mx-1">•</span> <a href="{{ url_for('index') }}#terms" class="text-light text-decoration-none">Terms</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    
    <!-- Global Scripts -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Function to handle logout
            function handleLogout(e) {
                e.preventDefault();
                
                fetch('/api/logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.href = '/';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
            }
            
            // Handle navbar logout button if it exists
            const navLogoutBtn = document.getElementById('navLogoutBtn');
            if (navLogoutBtn) {
                navLogoutBtn.addEventListener('click', handleLogout);
            }
            
            // Handle footer logout button if it exists
            const footerLogoutBtn = document.getElementById('footerLogoutBtn');
            if (footerLogoutBtn) {
                footerLogoutBtn.addEventListener('click', handleLogout);
            }
        });
    </script>
    
    {% block extra_scripts %}{% endblock %}
</body>
</html>
