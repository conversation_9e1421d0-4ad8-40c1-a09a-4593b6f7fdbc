{% extends "base.html" %}

{% block title %}TCG Alert - Login{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0"><i class="fas fa-sign-in-alt me-2"></i>Login</h3>
                </div>
                <div class="card-body p-4">
                    <div class="alert alert-danger d-none" id="loginError"></div>
                    <form id="loginForm">
                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="rememberMe">
                            <label class="form-check-label" for="rememberMe">Remember me</label>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-sign-in-alt me-2"></i>Login
                            </button>
                        </div>
                    </form>
                    <div class="mt-3 text-center">
                        <a href="/forgot-password" class="text-decoration-none">Forgot your password?</a>
                    </div>
                </div>
                <div class="card-footer bg-light text-center">
                    <p class="mb-0">Don't have an account? <a href="/register" class="text-decoration-none">Register now</a></p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const loginForm = document.getElementById('loginForm');
        const loginError = document.getElementById('loginError');

        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Clear previous errors
            loginError.classList.add('d-none');
            
            // Get form data
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            // Validate form
            if (!email || !password) {
                loginError.textContent = 'Please fill in all fields';
                loginError.classList.remove('d-none');
                return;
            }
            
            // Send login request
            fetch('/api/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    email: email,
                    password: password
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    loginError.textContent = data.error;
                    loginError.classList.remove('d-none');
                } else {
                    // Redirect to dashboard on successful login
                    window.location.href = '/dashboard';
                }
            })
            .catch(error => {
                loginError.textContent = 'An error occurred. Please try again.';
                loginError.classList.remove('d-none');
                console.error('Error:', error);
            });
        });
    });
</script>
{% endblock %}
