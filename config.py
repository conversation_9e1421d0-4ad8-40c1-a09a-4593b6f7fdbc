import os
from urllib.parse import quote_plus

class Config:
    # MongoDB Configuration
    MONGO_USERNAME = "admin"
    MONGO_PASSWORD = "Reggie2805!"
    MONGO_HOST = "*************"
    MONGO_PORT = "27017"
    MONGO_DB = "test"
    MONG<PERSON>_AUTH_SOURCE = "admin"
    
    # URL encode the password to handle special characters
    ENCODED_PASSWORD = quote_plus(MONGO_PASSWORD)
    
    # MongoDB URI
    MONGO_URI = f"mongodb://{MONGO_USERNAME}:{ENCODED_PASSWORD}@{MONGO_HOST}:{MONGO_PORT}/{MONGO_DB}?authSource={MONGO_AUTH_SOURCE}"
    
    # Flask Configuration
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'tcg-tools-secret-key-2023'
    DEBUG = True
    
    # Mailgun Configuration
    MAILGUN_API_KEY = os.environ.get('MAILGUN_API_KEY', '**************************************************')
    MAILGUN_DOMAIN = os.environ.get('MAILGUN_DOMAIN', 'tcgsync.com')
    MAILGUN_BASE_URL = 'https://api.eu.mailgun.net/v3'
    
    # Email Configuration
    MAIL_DEFAULT_SENDER = f'TCG Alert <noreply@{MAILGUN_DOMAIN}>'
    MAIL_MAX_EMAILS = 100
