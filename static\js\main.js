// TCG Tools - Main JavaScript File

// Global variables
let currentPage = 1;
let isLoading = false;
let currentSearch = '';
let selectedCard = null;

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
    initializeTooltips();
    loadGames();
});

function setupEventListeners() {
    // Search functionality
    const searchInput = document.getElementById('searchInput');
    const searchBtn = document.getElementById('searchBtn');
    const loadMoreBtn = document.getElementById('loadMoreBtn');

    if (searchInput && searchBtn) {
        searchBtn.addEventListener('click', performSearch);
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
    }

    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', loadMoreCards);
    }

    // Dropdown functionality
    setupDropdownEventListeners();

    // Search mode toggle
    setupSearchModeToggle();

    // Set notification buttons
    setupNotificationButtons();

    // Modal event listeners
    setupModalEventListeners();
}

function setupDropdownEventListeners() {
    const gameSelect = document.getElementById('gameSelect');
    const expansionSelect = document.getElementById('expansionSelect');
    const cardSelect = document.getElementById('cardSelect');
    const viewCardBtn = document.getElementById('viewCardBtn');
    const clearFiltersBtn = document.getElementById('clearFiltersBtn');

    if (gameSelect) {
        gameSelect.addEventListener('change', function() {
            const selectedGame = this.value;
            if (selectedGame) {
                loadExpansions(selectedGame);
                resetExpansionAndCard();
            } else {
                resetExpansionAndCard();
            }
        });
    }

    if (expansionSelect) {
        expansionSelect.addEventListener('change', function() {
            const selectedGame = gameSelect.value;
            const selectedExpansion = this.value;
            if (selectedGame && selectedExpansion) {
                loadCards(selectedGame, selectedExpansion);
                resetCardSelection();
            } else {
                resetCardSelection();
            }
        });
    }

    if (cardSelect) {
        cardSelect.addEventListener('change', function() {
            const selectedCard = this.value;
            if (viewCardBtn) {
                viewCardBtn.disabled = !selectedCard;
            }
        });
    }

    if (viewCardBtn) {
        viewCardBtn.addEventListener('click', function() {
            const selectedCardId = cardSelect.value;
            if (selectedCardId) {
                viewSelectedCard(selectedCardId);
            }
        });
    }

    if (clearFiltersBtn) {
        clearFiltersBtn.addEventListener('click', function() {
            clearAllFilters();
        });
    }
}

function setupSearchModeToggle() {
    // Search mode toggle functionality has been removed
    // Only dropdown filters are now available
}

function setupNotificationButtons() {
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('set-notification-btn') || 
            e.target.closest('.set-notification-btn')) {
            
            const btn = e.target.classList.contains('set-notification-btn') ? 
                        e.target : e.target.closest('.set-notification-btn');
            
            selectedCard = {
                id: btn.dataset.cardId,
                name: btn.dataset.cardName,
                currentPrice: parseFloat(btn.dataset.currentPrice) || 0
            };
            
            openNotificationModal();
        }
    });
}

function setupModalEventListeners() {
    const saveNotificationBtn = document.getElementById('saveNotificationBtn');
    const notificationModal = document.getElementById('notificationModal');
    
    if (saveNotificationBtn) {
        saveNotificationBtn.addEventListener('click', saveNotification);
    }

    if (notificationModal) {
        notificationModal.addEventListener('hidden.bs.modal', function() {
            resetNotificationForm();
        });
    }
}

function performSearch() {
    const searchInput = document.getElementById('searchInput');
    const query = searchInput.value.trim();
    
    currentSearch = query;
    currentPage = 1;
    
    if (query === '') {
        loadCards(true); // Load all cards
    } else {
        searchCards(query);
    }
}

function searchCards(query) {
    if (isLoading) return;
    
    isLoading = true;
    showLoading();
    
    fetch(`/api/cards?search=${encodeURIComponent(query)}&limit=20`)
        .then(response => response.json())
        .then(cards => {
            displayCards(cards, true);
            hideLoading();
            isLoading = false;
        })
        .catch(error => {
            console.error('Error searching cards:', error);
            showAlert('Error searching cards. Please try again.', 'danger');
            hideLoading();
            isLoading = false;
        });
}

function loadCards(replace = false) {
    if (isLoading) return;
    
    isLoading = true;
    if (replace) showLoading();
    
    const skip = replace ? 0 : (currentPage - 1) * 20;
    
    fetch(`/api/cards?page=${currentPage}&limit=20`)
        .then(response => response.json())
        .then(cards => {
            displayCards(cards, replace);
            if (replace) hideLoading();
            isLoading = false;
        })
        .catch(error => {
            console.error('Error loading cards:', error);
            showAlert('Error loading cards. Please try again.', 'danger');
            if (replace) hideLoading();
            isLoading = false;
        });
}

function loadMoreCards() {
    if (currentSearch) {
        // For search results, we don't implement pagination yet
        showAlert('Load more is not available for search results. Try refining your search.', 'info');
        return;
    }
    
    currentPage++;
    loadCards(false);
}

function displayCards(cards, replace = false) {
    const cardsGrid = document.getElementById('cardsGrid');
    const noResults = document.getElementById('noResults');
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    
    if (cards.length === 0 && replace) {
        cardsGrid.innerHTML = '';
        noResults.classList.remove('d-none');
        loadMoreBtn.style.display = 'none';
        return;
    }
    
    noResults.classList.add('d-none');
    loadMoreBtn.style.display = 'block';
    
    const cardsHTML = cards.map(card => createCardHTML(card)).join('');
    
    if (replace) {
        cardsGrid.innerHTML = cardsHTML;
    } else {
        cardsGrid.insertAdjacentHTML('beforeend', cardsHTML);
    }
    
    // Hide load more button if we got fewer cards than requested
    if (cards.length < 20) {
        loadMoreBtn.style.display = 'none';
    }
}

function createCardHTML(card) {
    const cardName = card.name || 'Unknown Card';
    const cardSet = card.set || '';
    const cardType = card.type || '';
    const cardPrice = card.price || 0;

    return `
        <div class="card-item fade-in">
            <div class="modern-card">
                <div class="card-header-modern">
                    <div class="card-type-badge">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="card-actions">
                        <button class="action-btn favorite-btn" title="Add to favorites">
                            <i class="far fa-heart"></i>
                        </button>
                    </div>
                </div>

                <div class="card-content">
                    <h6 class="card-name" title="${cardName}">
                        ${cardName}
                    </h6>

                    <div class="card-meta">
                        ${cardSet ? `
                            <span class="meta-item">
                                <i class="fas fa-layer-group"></i>
                                ${cardSet}
                            </span>
                        ` : ''}

                        ${cardType ? `
                            <span class="meta-item">
                                <i class="fas fa-tag"></i>
                                ${cardType}
                            </span>
                        ` : ''}
                    </div>

                    ${cardPrice > 0 ? `
                        <div class="card-price">
                            <span class="price-label">Current Price</span>
                            <span class="price-value">$${cardPrice.toFixed(2)}</span>
                        </div>
                    ` : ''}
                </div>

                <div class="card-footer-modern">
                    <button class="notification-btn set-notification-btn"
                            data-card-id="${card._id}"
                            data-card-name="${cardName}"
                            data-current-price="${cardPrice}">
                        <i class="fas fa-bell"></i>
                        <span>Set Alert</span>
                    </button>
                </div>
            </div>
        </div>
    `;
}

function openNotificationModal() {
    if (!selectedCard) return;
    
    const modal = new bootstrap.Modal(document.getElementById('notificationModal'));
    const modalCardName = document.getElementById('modalCardName');
    const targetPriceInput = document.getElementById('targetPrice');
    
    modalCardName.textContent = selectedCard.name;
    
    // Set a suggested target price (10% below current price)
    if (selectedCard.currentPrice > 0) {
        const suggestedPrice = (selectedCard.currentPrice * 0.9).toFixed(2);
        targetPriceInput.value = suggestedPrice;
    }
    
    modal.show();
}

function saveNotification() {
    const targetPrice = document.getElementById('targetPrice').value;
    const userEmail = document.getElementById('userEmail').value;
    
    if (!selectedCard || !targetPrice) {
        showAlert('Please fill in all required fields.', 'warning');
        return;
    }
    
    const notificationData = {
        card_id: selectedCard.id,
        card_name: selectedCard.name,
        target_price: parseFloat(targetPrice),
        user_email: userEmail || null
    };
    
    // Disable the save button to prevent double submission
    const saveBtn = document.getElementById('saveNotificationBtn');
    const originalText = saveBtn.innerHTML;
    saveBtn.disabled = true;
    saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Saving...';
    
    fetch('/api/notifications', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(notificationData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('Notification created successfully!', 'success');
            const modal = bootstrap.Modal.getInstance(document.getElementById('notificationModal'));
            modal.hide();
        } else {
            showAlert(data.error || 'Error creating notification.', 'danger');
        }
    })
    .catch(error => {
        console.error('Error creating notification:', error);
        showAlert('Error creating notification. Please try again.', 'danger');
    })
    .finally(() => {
        saveBtn.disabled = false;
        saveBtn.innerHTML = originalText;
    });
}

function resetNotificationForm() {
    document.getElementById('targetPrice').value = '';
    document.getElementById('userEmail').value = '';
    selectedCard = null;
}

function showLoading() {
    const loadingSpinner = document.getElementById('loadingSpinner');
    const cardsContainer = document.getElementById('cardsContainer');
    
    if (loadingSpinner) loadingSpinner.classList.remove('d-none');
    if (cardsContainer) cardsContainer.classList.add('d-none');
}

function hideLoading() {
    const loadingSpinner = document.getElementById('loadingSpinner');
    const cardsContainer = document.getElementById('cardsContainer');
    
    if (loadingSpinner) loadingSpinner.classList.add('d-none');
    if (cardsContainer) cardsContainer.classList.remove('d-none');
}

function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container');
    if (container) {
        container.insertBefore(alertDiv, container.firstChild);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
}

function initializeTooltips() {
    // Initialize Bootstrap tooltips if any exist
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// Utility functions
function formatPrice(price) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(price);
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

// Dropdown data loading functions
function loadGames() {
    const gameSelect = document.getElementById('gameSelect');
    if (!gameSelect) return;

    fetch('/api/games')
        .then(response => response.json())
        .then(games => {
            // Sort games alphabetically
            games.sort((a, b) => a.localeCompare(b));
            
            gameSelect.innerHTML = '<option value="">Choose a game...</option>';
            games.forEach(game => {
                const option = document.createElement('option');
                option.value = game;
                option.textContent = game;
                gameSelect.appendChild(option);
            });
        })
        .catch(error => {
            console.error('Error loading games:', error);
            gameSelect.innerHTML = '<option value="">Error loading games</option>';
        });
}

function loadExpansions(game) {
    const expansionSelect = document.getElementById('expansionSelect');
    if (!expansionSelect) return;

    expansionSelect.innerHTML = '<option value="">Loading expansions...</option>';
    expansionSelect.disabled = true;

    fetch(`/api/expansions/${encodeURIComponent(game)}`)
        .then(response => response.json())
        .then(expansions => {
            // Sort expansions alphabetically
            expansions.sort((a, b) => a.localeCompare(b));
            
            expansionSelect.innerHTML = '<option value="">Choose an expansion...</option>';
            expansions.forEach(expansion => {
                const option = document.createElement('option');
                option.value = expansion;
                option.textContent = expansion;
                expansionSelect.appendChild(option);
            });
            expansionSelect.disabled = false;
        })
        .catch(error => {
            console.error('Error loading expansions:', error);
            expansionSelect.innerHTML = '<option value="">Error loading expansions</option>';
            expansionSelect.disabled = false;
        });
}

function loadCards(game, expansion) {
    const cardSelect = document.getElementById('cardSelect');
    if (!cardSelect) return;

    cardSelect.innerHTML = '<option value="">Loading cards...</option>';
    cardSelect.disabled = true;

    fetch(`/api/cards-by-expansion/${encodeURIComponent(game)}/${encodeURIComponent(expansion)}`)
        .then(response => response.json())
        .then(cards => {
            // Sort cards alphabetically by name
            cards.sort((a, b) => {
                const nameA = a.name || 'Unknown Card';
                const nameB = b.name || 'Unknown Card';
                return nameA.localeCompare(nameB);
            });
            
            cardSelect.innerHTML = '<option value="">Choose a card...</option>';
            cards.forEach(card => {
                const option = document.createElement('option');
                option.value = card._id;
                option.textContent = card.name || 'Unknown Card';
                cardSelect.appendChild(option);
            });
            cardSelect.disabled = false;
        })
        .catch(error => {
            console.error('Error loading cards:', error);
            cardSelect.innerHTML = '<option value="">Error loading cards</option>';
            cardSelect.disabled = false;
        });
}

function resetExpansionAndCard() {
    const expansionSelect = document.getElementById('expansionSelect');
    const cardSelect = document.getElementById('cardSelect');
    const viewCardBtn = document.getElementById('viewCardBtn');

    if (expansionSelect) {
        expansionSelect.innerHTML = '<option value="">Choose an expansion...</option>';
        expansionSelect.disabled = true;
    }

    if (cardSelect) {
        cardSelect.innerHTML = '<option value="">Choose a card...</option>';
        cardSelect.disabled = true;
    }

    if (viewCardBtn) {
        viewCardBtn.disabled = true;
    }
}

function resetCardSelection() {
    const cardSelect = document.getElementById('cardSelect');
    const viewCardBtn = document.getElementById('viewCardBtn');

    if (cardSelect) {
        cardSelect.innerHTML = '<option value="">Choose a card...</option>';
        cardSelect.disabled = true;
    }

    if (viewCardBtn) {
        viewCardBtn.disabled = true;
    }
}

function clearAllFilters() {
    const gameSelect = document.getElementById('gameSelect');
    const expansionSelect = document.getElementById('expansionSelect');
    const cardSelect = document.getElementById('cardSelect');
    const viewCardBtn = document.getElementById('viewCardBtn');

    if (gameSelect) gameSelect.value = '';
    if (expansionSelect) {
        expansionSelect.innerHTML = '<option value="">Choose an expansion...</option>';
        expansionSelect.disabled = true;
    }
    if (cardSelect) {
        cardSelect.innerHTML = '<option value="">Choose a card...</option>';
        cardSelect.disabled = true;
    }
    if (viewCardBtn) viewCardBtn.disabled = true;

    // Hide card details section
    hideCardDetails();
}

function viewSelectedCard(cardId) {
    const cardDetailsSection = document.getElementById('cardDetailsSection');
    const cardDetailsContent = document.getElementById('cardDetailsContent');

    // Show loading state
    cardDetailsSection.classList.remove('d-none');
    cardDetailsContent.innerHTML = `
        <div class="text-center p-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 mb-0">Loading card details...</p>
        </div>
    `;

    // Scroll to card details section
    cardDetailsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });

    fetch(`/api/card/${cardId}`)
        .then(response => response.json())
        .then(card => {
            if (card && !card.error) {
                console.log('Card received:', card.name, 'ProductId:', card.productId);
                console.log('PriceVariants field:', card.priceVariants);
                displayCardDetails(card);
                // Store selected card for potential notification setup
                // Find the lowest price among all variants for notification setup
                let lowestPrice = 0;
                if (card.priceVariants && card.priceVariants.length > 0) {
                    lowestPrice = Math.min(...card.priceVariants.map(v => v.lowPrice));
                }

                selectedCard = {
                    id: card._id,
                    name: card.name || 'Unknown Card',
                    currentPrice: lowestPrice
                };
            } else {
                showCardError('Card not found or error loading card details.');
            }
        })
        .catch(error => {
            console.error('Error loading card details:', error);
            showCardError('Error loading card details. Please try again.');
        });
}

function displayCardDetails(card) {
    const cardDetailsContent = document.getElementById('cardDetailsContent');

    const cardHtml = `
        <div class="card-details-flex">
            <div class="card-image-container">
                ${card.imageUrl ? `<img src="${card.imageUrl}" alt="${card.name || 'Card Image'}" class="card-detail-image">` : 
                '<div class="no-image-placeholder">No image available</div>'}
            </div>
            <div class="card-info-grid">
                <div class="card-info-item">
                    <span class="card-info-label">Card Name</span>
                    <span class="card-info-value">${card.name || 'Unknown'}</span>
                </div>
                <div class="card-info-item">
                    <span class="card-info-label">Game</span>
                    <span class="card-info-value">${card.gameName || 'Unknown'}</span>
                </div>
                <div class="card-info-item">
                    <span class="card-info-label">Expansion</span>
                    <span class="card-info-value">${card.expansionName || 'Unknown'}</span>
                </div>
                <div class="card-info-item">
                    <span class="card-info-label">TCGPlayer ID</span>
                    <span class="card-info-value">${card.productId || 'N/A'}</span>
                </div>
                <div class="card-info-item card-prices-item">
                    <span class="card-info-label">Current Prices</span>
                    <div class="card-info-value">
                        ${generatePriceVariantsHtml(card.priceVariants || [])}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Lowest Listings Section -->
        ${card.lowestListings && card.lowestListings.length > 0 ? `
        <div class="card-listings-section mt-4">
            <h4 class="section-title">Lowest Listings</h4>
            
            ${(() => {
                // Group listings by printing type
                const listingsByPrinting = {};
                card.lowestListings.forEach(listing => {
                    const printing = listing.printing || 'Unknown';
                    if (!listingsByPrinting[printing]) {
                        listingsByPrinting[printing] = [];
                    }
                    listingsByPrinting[printing].push(listing);
                });
                
                // Sort each group by price
                Object.keys(listingsByPrinting).forEach(printing => {
                    listingsByPrinting[printing].sort((a, b) => (a.price + a.shipping) - (b.price + b.shipping));
                    // Limit to top 10
                    listingsByPrinting[printing] = listingsByPrinting[printing].slice(0, 10);
                });
                
                // Get all available printing types
                const printingTypes = Object.keys(listingsByPrinting);
                
                // Create tabs for each printing type
                return `
                <div class="printing-tabs mb-3">
                    <div class="btn-group" role="group" aria-label="Printing types">
                        ${printingTypes.map((printing, index) => `
                            <button type="button" class="btn ${index === 0 ? 'btn-primary' : 'btn-outline-primary'}" 
                                    onclick="showPrintingTab('${printing}')" 
                                    id="tab-${printing.replace(/\s+/g, '-').toLowerCase()}">
                                ${printing}
                            </button>
                        `).join('')}
                    </div>
                </div>
                
                ${printingTypes.map((printing, index) => `
                    <div class="listings-table-container printing-tab ${index === 0 ? '' : 'd-none'}" 
                         id="listings-${printing.replace(/\s+/g, '-').toLowerCase()}">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Seller</th>
                                    <th>Condition</th>
                                    <th>Price</th>
                                    <th>Quantity</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${(() => {
                                    // Determine trend price for this printing variant
                                    let trendPrice = 0;
                                    if (card.priceVariants && card.priceVariants.length > 0) {
                                        const matchingVariant = card.priceVariants.find(v => v.variant === printing) || card.priceVariants[0];
                                        trendPrice = matchingVariant.lowPrice;
                                    }
                                    
                                    return listingsByPrinting[printing].map(listing => {
                                        const totalPrice = listing.price + listing.shipping;
                                        // Determine if price is below/matching trend (green) or above trend (red)
                                        const priceClass = totalPrice <= trendPrice * 1.05 ? 'text-success' : 'text-danger';
                                        
                                        return `
                                        <tr class="${priceClass}">
                                            <td>${listing.seller}</td>
                                            <td>${listing.condition}</td>
                                            <td>$${listing.price.toFixed(2)}</td>
                                            <td>${listing.quantity}</td>
                                        </tr>
                                        `;
                                    }).join('');
                                })()}
                            </tbody>
                        </table>
                    </div>
                `).join('')}
                `;
            })()}
        </div>
        ` : ''}
        
        <div class="card-actions mt-4">
            <button class="btn btn-primary btn-lg" onclick="openNotificationModal()">
                <i class="fas fa-bell me-2"></i>Set Price Alert
            </button>
            <button class="btn btn-outline-secondary btn-lg ms-3" onclick="hideCardDetails()">
                <i class="fas fa-times me-2"></i>Close
            </button>
        </div>
    `;

    cardDetailsContent.innerHTML = cardHtml;
}

function showCardError(message) {
    const cardDetailsContent = document.getElementById('cardDetailsContent');
    cardDetailsContent.innerHTML = `
        <div class="text-center p-4">
            <div class="text-danger mb-3">
                <i class="fas fa-exclamation-triangle fa-2x"></i>
            </div>
            <p class="text-danger mb-3">${message}</p>
            <button class="btn btn-outline-secondary" onclick="hideCardDetails()">
                <i class="fas fa-times me-2"></i>Close
            </button>
        </div>
    `;
}

function generatePriceVariantsHtml(priceVariants) {
    if (!priceVariants || priceVariants.length === 0) {
        return '<span class="no-price-data">No pricing data available</span>';
    }

    return priceVariants.map(variant =>
        `<div class="price-variant">
            <span class="variant-name">${variant.variant}:</span>
            <span class="variant-price">$${variant.lowPrice.toFixed(2)}</span>
        </div>`
    ).join('');
}

function hideCardDetails() {
    const cardDetailsSection = document.getElementById('cardDetailsSection');
    cardDetailsSection.classList.add('d-none');
}

// Function to switch between printing tabs
window.showPrintingTab = function(printing) {
    // Normalize the printing name for use in IDs
    const normalizedPrinting = printing.replace(/\s+/g, '-').toLowerCase();
    
    // Hide all tabs
    document.querySelectorAll('.printing-tab').forEach(tab => {
        tab.classList.add('d-none');
    });
    
    // Show the selected tab
    const selectedTab = document.getElementById(`listings-${normalizedPrinting}`);
    if (selectedTab) {
        selectedTab.classList.remove('d-none');
    }
    
    // Update button styles
    document.querySelectorAll('.printing-tabs .btn').forEach(btn => {
        btn.classList.remove('btn-primary');
        btn.classList.add('btn-outline-primary');
    });
    
    // Highlight the selected button
    const selectedBtn = document.getElementById(`tab-${normalizedPrinting}`);
    if (selectedBtn) {
        selectedBtn.classList.remove('btn-outline-primary');
        selectedBtn.classList.add('btn-primary');
    }
}

// Export functions for use in other scripts
window.TCGTools = {
    showAlert,
    formatPrice,
    formatDate,
    loadGames,
    loadExpansions,
    loadCards,
    showPrintingTab
};
