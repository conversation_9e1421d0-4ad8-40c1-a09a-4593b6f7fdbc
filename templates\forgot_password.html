{% extends "base.html" %}

{% block title %}TCG Alert - Forgot Password{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0"><i class="fas fa-key me-2"></i>Reset Password</h3>
                </div>
                <div class="card-body p-4">
                    <div class="alert alert-danger d-none" id="resetError"></div>
                    <div class="alert alert-success d-none" id="resetSuccess"></div>
                    <p class="mb-4">Enter your email address below and we'll send you a link to reset your password.</p>
                    <form id="forgotPasswordForm">
                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-2"></i>Send Reset Link
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-footer bg-light text-center">
                    <p class="mb-0">Remember your password? <a href="/login" class="text-decoration-none">Back to Login</a></p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const forgotPasswordForm = document.getElementById('forgotPasswordForm');
        const resetError = document.getElementById('resetError');
        const resetSuccess = document.getElementById('resetSuccess');

        forgotPasswordForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Clear previous messages
            resetError.classList.add('d-none');
            resetSuccess.classList.add('d-none');
            
            // Get form data
            const email = document.getElementById('email').value;
            
            // Validate form
            if (!email) {
                resetError.textContent = 'Please enter your email address';
                resetError.classList.remove('d-none');
                return;
            }
            
            // Send password reset request
            fetch('/api/reset-password-request', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    email: email
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    resetError.textContent = data.error;
                    resetError.classList.remove('d-none');
                } else {
                    // Show success message
                    resetSuccess.textContent = data.message || 'If the email exists in our system, a password reset link will be sent.';
                    resetSuccess.classList.remove('d-none');
                    
                    // Clear form
                    forgotPasswordForm.reset();
                }
            })
            .catch(error => {
                resetError.textContent = 'An error occurred. Please try again.';
                resetError.classList.remove('d-none');
                console.error('Error:', error);
            });
        });
    });
</script>
{% endblock %}
