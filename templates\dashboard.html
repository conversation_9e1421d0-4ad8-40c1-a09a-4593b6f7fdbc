{% extends "base.html" %}

{% block title %}TCG Alert - Dashboard{% endblock %}

{% block extra_head %}
<style>
    /* Custom Dashboard Styles */
    .dashboard-container {
        background-color: #f8f9fa;
        min-height: calc(100vh - 56px - 240px); /* Adjust based on navbar and footer height */
    }
    
    .sidebar {
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        position: sticky;
        top: 80px;
    }
    
    .sidebar-link {
        color: #495057;
        border-radius: 8px;
        margin: 4px 0;
        transition: all 0.2s ease;
    }
    
    .sidebar-link:hover {
        background-color: rgba(13, 110, 253, 0.05);
        color: #0d6efd;
    }
    
    .sidebar-link.active {
        background-color: #0d6efd;
        color: white;
    }
    
    .sidebar-link i {
        width: 24px;
        text-align: center;
    }
    
    .stat-card {
        border: none;
        border-radius: 12px;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
    }
    
    .stat-icon {
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 12px;
    }

    .table-card {
        border: none;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    }

    /* Enhanced table styling */
    .results-table {
        margin-bottom: 0;
        border-collapse: separate;
        border-spacing: 0;
    }

    .results-table thead th {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border: none;
        font-weight: 600;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        color: #495057;
        padding: 1rem 0.75rem;
        vertical-align: middle;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .results-table thead th:first-child {
        border-top-left-radius: 12px;
    }

    .results-table thead th:last-child {
        border-top-right-radius: 12px;
    }

    .results-table tbody tr {
        transition: all 0.2s ease;
        border: none;
    }

    .results-table tbody tr:hover {
        background-color: rgba(13, 110, 253, 0.04);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .results-table tbody td {
        padding: 1rem 0.75rem;
        vertical-align: middle;
        border: none;
        border-bottom: 1px solid #f1f3f4;
    }

    .results-table tbody tr:last-child td {
        border-bottom: none;
    }

    /* Card thumbnail styling */
    .card-thumbnail {
        width: 40px;
        height: 56px;
        object-fit: cover;
        border-radius: 4px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
        transition: transform 0.2s ease;
    }

    .card-thumbnail:hover {
        transform: scale(1.05);
    }

    /* Card name styling */
    .card-name {
        font-weight: 600;
        color: #2c3e50;
        text-decoration: none;
        transition: color 0.2s ease;
        word-wrap: break-word;
        overflow-wrap: break-word;
        hyphens: auto;
        line-height: 1.3;
    }

    .card-name:hover {
        color: #0d6efd;
    }

    /* Set name styling */
    .set-name {
        color: #6c757d;
        font-size: 0.875rem;
        font-weight: 500;
        word-wrap: break-word;
        overflow-wrap: break-word;
        hyphens: auto;
        line-height: 1.3;
    }

    /* Card number styling */
    .card-number {
        font-family: 'Courier New', monospace;
        font-weight: 600;
        color: #495057;
        background: #f8f9fa;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.8rem;
    }

    /* Price styling */
    .price-display {
        font-weight: 700;
        font-size: 1.1rem;
        color: #28a745;
    }

    .price-unavailable {
        color: #6c757d;
        font-style: italic;
        font-size: 0.9rem;
    }

    /* Variants badge styling */
    .variants-badge {
        background: linear-gradient(135deg, #e3f2fd, #bbdefb);
        color: #1976d2;
        border: 1px solid #90caf9;
        font-weight: 600;
        font-size: 0.75rem;
        padding: 0.4rem 0.8rem;
        border-radius: 20px;
    }

    /* Action buttons styling */
    .action-buttons {
        display: flex;
        gap: 0.5rem;
        align-items: center;
    }

    .track-btn {
        background: linear-gradient(135deg, #0d6efd, #0b5ed7);
        border: none;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        font-weight: 600;
        font-size: 0.875rem;
        transition: all 0.2s ease;
        box-shadow: 0 2px 4px rgba(13, 110, 253, 0.3);
    }

    .track-btn:hover {
        background: linear-gradient(135deg, #0b5ed7, #0a58ca);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(13, 110, 253, 0.4);
        color: white;
    }

    .expand-btn {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        color: #6c757d;
        padding: 0.5rem;
        border-radius: 6px;
        transition: all 0.2s ease;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .expand-btn:hover {
        background: #e9ecef;
        border-color: #adb5bd;
        color: #495057;
        transform: translateY(-1px);
    }

    /* Detail row styling */
    .detail-row {
        background: #f8f9fa !important;
        border-top: 2px solid #e9ecef !important;
    }

    .detail-row td {
        padding: 1.5rem !important;
        border-bottom: 2px solid #e9ecef !important;
    }

    /* Printing tabs styling */
    .printing-tabs .btn-group .btn {
        border-radius: 6px !important;
        margin-right: 0.5rem;
        font-weight: 600;
        font-size: 0.875rem;
        padding: 0.5rem 1rem;
        transition: all 0.2s ease;
    }

    .printing-tabs .btn-outline-primary {
        border-color: #dee2e6;
        color: #6c757d;
        background: white;
    }

    .printing-tabs .btn-outline-primary:hover {
        background: #e9ecef;
        border-color: #adb5bd;
        color: #495057;
    }

    .printing-tabs .btn-primary {
        background: linear-gradient(135deg, #0d6efd, #0b5ed7);
        border-color: #0d6efd;
        box-shadow: 0 2px 4px rgba(13, 110, 253, 0.3);
    }

    /* Listings table styling */
    .listings-table-container .table {
        margin-bottom: 0;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .listings-table-container .table thead th {
        background: #e9ecef;
        border: none;
        font-weight: 600;
        font-size: 0.875rem;
        color: #495057;
        padding: 0.75rem;
    }

    .listings-table-container .table tbody td {
        padding: 0.75rem;
        border-color: #f1f3f4;
        vertical-align: middle;
    }

    .listings-table-container .table tbody tr:hover {
        background-color: rgba(13, 110, 253, 0.04);
    }

    /* Enhanced responsive design */
    @media (max-width: 768px) {
        .results-table thead th {
            padding: 0.75rem 0.5rem;
            font-size: 0.8rem;
        }

        .results-table tbody td {
            padding: 0.75rem 0.5rem;
        }

        .card-thumbnail {
            width: 35px;
            height: 49px;
        }

        .action-buttons {
            flex-direction: column;
            gap: 0.25rem;
        }

        .track-btn {
            font-size: 0.8rem;
            padding: 0.4rem 0.8rem;
        }
    }
    
    .action-card {
        border: none;
        border-radius: 12px;
        transition: transform 0.2s ease;
    }
    
    .action-card:hover {
        transform: translateY(-5px);
    }
    
    .action-btn {
        border-radius: 10px;
        padding: 1rem;
        transition: all 0.2s ease;
    }
    
    .subscription-card {
        border: none;
        border-radius: 12px;
        background: linear-gradient(135deg, #198754, #157347);
        color: white;
    }
    
    .upgrade-btn {
        border-radius: 8px;
        background-color: rgba(255, 255, 255, 0.2);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.4);
        transition: all 0.2s ease;
    }
    
    .upgrade-btn:hover {
        background-color: rgba(255, 255, 255, 0.3);
        color: white;
    }
    
    /* Sortable table styles */
    .sortable {
        cursor: pointer;
        position: relative;
        user-select: none;
    }
    
    .sortable:hover {
        background-color: rgba(13, 110, 253, 0.05);
    }
    
    .sortable.sort-asc .fa-sort:before {
        content: "\f0de"; /* fa-sort-up */
    }
    
    .sortable.sort-desc .fa-sort:before {
        content: "\f0dd"; /* fa-sort-down */
    }

    /* Filter box styles */
    #tableFilterContainer {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1rem;
    }

    #tableFilter {
        border-radius: 6px;
    }

    #clearFilter {
        border-radius: 0 6px 6px 0;
    }

    #filterResultsCount {
        font-size: 0.9rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container py-4">
    <div class="container-fluid">
        
        <div class="row g-4">
            <!-- Sidebar -->
            <div class="col-auto">
                <div class="sidebar p-2 mb-4">
                    <div class="nav flex-column">
                        <a href="{{ url_for('dashboard_page') }}" class="sidebar-link active nav-link py-2 px-3">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                        <a href="#" class="sidebar-link nav-link py-2 px-3">
                            <i class="fas fa-chart-line me-2"></i>Sales History
                        </a>
                        <a href="#" class="sidebar-link nav-link py-2 px-3">
                            <i class="fas fa-search-dollar me-2"></i>Card Checker
                        </a>
                        <a href="#" class="sidebar-link nav-link py-2 px-3">
                            <i class="fas fa-file-import me-2"></i>Bulk Import
                        </a>
                        <a href="#" class="sidebar-link nav-link py-2 px-3">
                            <i class="fas fa-tags me-2"></i>Deal Finder
                        </a>
                        <a href="#" class="sidebar-link nav-link py-2 px-3">
                            <i class="fas fa-store me-2"></i>Support Local
                        </a>
                        <a href="#" class="sidebar-link nav-link py-2 px-3">
                            <i class="fas fa-map-marker-alt me-2"></i>Find LGS
                        </a>
                        <a href="#" class="sidebar-link nav-link py-2 px-3">
                            <i class="fas fa-calendar-alt me-2"></i>Event Finder
                        </a>
                        <a href="#" class="sidebar-link nav-link py-2 px-3">
                            <i class="fas fa-cog me-2"></i>Settings
                        </a>
                        <a href="#" class="sidebar-link nav-link py-2 px-3 text-danger" id="logoutBtn">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col">

                <!-- Stats Row -->
                <div class="row g-4 mb-4">
                    <div class="col-md-4">
                        <div class="stat-card card h-100">
                            <div class="card-body p-4">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <p class="text-muted mb-1 small text-uppercase fw-bold">Active Notifications</p>
                                        <h3 class="fw-bold mb-0" id="activeNotifications">0</h3>
                                        <a href="/notifications" class="btn btn-sm btn-outline-primary mt-2">
                                            <i class="fas fa-table me-1"></i>View All Alerts
                                        </a>
                                    </div>
                                    <div class="stat-icon bg-primary bg-opacity-10">
                                        <i class="fas fa-bell fa-lg text-primary"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="stat-card card h-100">
                            <div class="card-body p-4">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <p class="text-muted mb-1 small text-uppercase fw-bold">Triggered Alerts</p>
                                        <h3 class="fw-bold mb-0" id="triggeredAlerts">0</h3>
                                    </div>
                                    <div class="stat-icon bg-success bg-opacity-10">
                                        <i class="fas fa-check-circle fa-lg text-success"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="stat-card card h-100">
                            <div class="card-body p-4">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <p class="text-muted mb-1 small text-uppercase fw-bold">Slots Available</p>
                                        <h3 class="fw-bold mb-0" id="slotsAvailable">20</h3>
                                    </div>
                                    <div class="stat-icon bg-info bg-opacity-10">
                                        <i class="fas fa-layer-group fa-lg text-info"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Dashboard content continues here -->
                
                <!-- Card Search -->
                <div class="row g-4">
                    <div class="col-md-12">
                        <div class="action-card card h-100">
                            <div class="card-body p-4">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="bg-primary bg-opacity-10 p-3 rounded-circle me-3">
                                        <i class="fas fa-bell text-primary"></i>
                                    </div>
                                    <h5 class="fw-bold mb-0">Create an Alert</h5>
                                </div>
                                <div class="mb-3">
                                    <div class="row g-3">
                                        <div class="col-md-4">
                                            <label for="gameSelect" class="form-label">Select Game</label>
                                            <select class="form-select" id="gameSelect">
                                                <option value="">Choose a game...</option>
                                                <option value="loading" disabled>Loading games...</option>
                                            </select>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="expansionSelect" class="form-label">Select Expansion</label>
                                            <select class="form-select" id="expansionSelect" disabled>
                                                <option value="">Choose an expansion...</option>
                                            </select>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="cardSearchInput" class="form-label">Search by Name</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control" id="cardSearchInput" placeholder="Search cards...">
                                                <button class="btn btn-primary" type="button" id="cardSearchButton">
                                                    <i class="fas fa-search"></i>
                                                </button>
                                            </div>
                                            <small class="text-muted">Supports regex patterns</small>
                                        </div>
                                    </div>
                                    
                                    <div class="row mt-2">
                                        <div class="col-12">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="includeSealedToggle">
                                                <label class="form-check-label" for="includeSealedToggle">Include Sealed Products</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Expansion Total Value Display -->
                                <div id="expansionValueContainer" class="mt-3" style="display: none;">
                                    <div class="card border-success">
                                        <div class="card-body p-3">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div class="flex-grow-1">
                                                    <div class="d-flex align-items-center mb-1">
                                                        <h6 class="card-title mb-0 text-success me-2">
                                                            <i class="fas fa-calculator me-2"></i>Complete Set Value
                                                        </h6>
                                                        <span id="valueSourceBadge" class="badge bg-success">Cached</span>
                                                    </div>
                                                    <p class="card-text mb-0">
                                                        <span class="text-muted">Total cost for one of each print type (Normal, Foil, etc.) for every card:</span>
                                                        <span class="fw-bold fs-4 text-success ms-2" id="expansionTotalValue">$0.00</span>
                                                    </p>
                                                    <small class="text-muted" id="expansionValueDetails">
                                                        Loading...
                                                    </small>
                                                    <div id="expansionValueMeta" class="mt-1" style="display: none;">
                                                        <small class="text-muted">
                                                            <i class="fas fa-clock me-1"></i>
                                                            <span id="calculatedAt">Calculated: Loading...</span>
                                                            <span class="ms-3">
                                                                <i class="fas fa-history me-1"></i>
                                                                <a href="#" id="viewHistoryLink" class="text-decoration-none">View History</a>
                                                            </span>
                                                        </small>
                                                    </div>
                                                    <div id="variantBreakdown" class="mt-2" style="display: none;">
                                                        <small class="text-muted d-block mb-1">Breakdown by print type:</small>
                                                        <div id="variantBreakdownContent"></div>
                                                    </div>
                                                </div>
                                                <div class="text-success">
                                                    <i class="fas fa-coins fa-2x opacity-50"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Expansion Value History Modal -->
                                <div class="modal fade" id="expansionHistoryModal" tabindex="-1" aria-labelledby="expansionHistoryModalLabel" aria-hidden="true">
                                    <div class="modal-dialog modal-lg">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="expansionHistoryModalLabel">
                                                    <i class="fas fa-chart-line me-2"></i>Expansion Value History
                                                </h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <div id="historyLoadingSpinner" class="text-center py-4">
                                                    <div class="spinner-border text-primary" role="status">
                                                        <span class="visually-hidden">Loading...</span>
                                                    </div>
                                                    <p class="mt-2 text-muted">Loading historical data...</p>
                                                </div>
                                                <div id="historyContent" style="display: none;">
                                                    <div class="mb-3">
                                                        <h6 id="historyExpansionName">Expansion Name</h6>
                                                        <p class="text-muted mb-0" id="historyDescription">Historical value data for the last 30 days</p>
                                                    </div>
                                                    <div class="table-responsive">
                                                        <table class="table table-sm">
                                                            <thead>
                                                                <tr>
                                                                    <th>Date</th>
                                                                    <th>Total Value</th>
                                                                    <th>Cards</th>
                                                                    <th>Variants</th>
                                                                    <th>Change</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody id="historyTableBody">
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                                <div id="historyError" style="display: none;" class="alert alert-warning">
                                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                                    <span id="historyErrorMessage">No historical data available for this expansion.</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Search Results Placeholder -->
                                <div id="searchPlaceholder" class="mt-4 text-center">
                                    <div class="py-5">
                                        <i class="fas fa-search fa-4x text-muted opacity-25 mb-3"></i>
                                        <h5 class="text-muted mb-2">Ready to Search</h5>
                                        <p class="text-muted mb-0">Select a game and expansion above, or use the search box to find specific cards</p>
                                    </div>
                                </div>

                                <div id="searchResults" class="mt-4" style="display: none;">
                                    <!-- Filter Box -->
                                    <div id="tableFilterContainer" class="mb-3" style="display: none;">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="input-group">
                                                    <span class="input-group-text">
                                                        <i class="fas fa-filter"></i>
                                                    </span>
                                                    <input type="text" class="form-control" id="tableFilter" placeholder="Filter cards by name...">
                                                    <button class="btn btn-outline-secondary" type="button" id="clearFilter">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                                <small class="text-muted">Type to filter the results below</small>
                                            </div>
                                            <div class="col-md-6 d-flex align-items-center">
                                                <span class="text-muted" id="filterResultsCount">Showing all results</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="table-responsive">
                                        <table class="results-table table">
                                            <thead>
                                                <tr>
                                                    <th style="width: 30px;"></th>
                                                    <th style="width: 60px;">Image</th>
                                                    <th class="sortable" data-sort="name" style="width: 25%;">
                                                        Name <i class="fas fa-sort ms-1"></i>
                                                    </th>
                                                    <th class="sortable" data-sort="expansion" style="width: 20%;">
                                                        Set <i class="fas fa-sort ms-1"></i>
                                                    </th>
                                                    <th class="sortable" data-sort="number" style="width: 80px;">
                                                        Number <i class="fas fa-sort ms-1"></i>
                                                    </th>
                                                    <th class="sortable" data-sort="price" style="width: 90px;">
                                                        Price <i class="fas fa-sort ms-1"></i>
                                                    </th>
                                                    <th style="width: 80px;">Variants</th>
                                                    <th style="width: 120px;">Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody id="searchResultsBody">
                                                <!-- Results will be populated here -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize dropdown selectors
        const gameSelect = document.getElementById('gameSelect');
        const expansionSelect = document.getElementById('expansionSelect');
        const cardSearchInput = document.getElementById('cardSearchInput');
        const cardSearchButton = document.getElementById('cardSearchButton');
        const searchResultsBody = document.getElementById('searchResultsBody');

        // Initialize filter elements
        const tableFilter = document.getElementById('tableFilter');
        const clearFilter = document.getElementById('clearFilter');
        const tableFilterContainer = document.getElementById('tableFilterContainer');
        const filterResultsCount = document.getElementById('filterResultsCount');

        // Initialize expansion value elements
        const valueSourceBadge = document.getElementById('valueSourceBadge');
        const expansionValueMeta = document.getElementById('expansionValueMeta');
        const calculatedAt = document.getElementById('calculatedAt');
        const viewHistoryLink = document.getElementById('viewHistoryLink');
        
        // Load initial data in parallel
        Promise.all([
            // Get user data
            fetch('/api/user').then(response => response.json()),
            // Load games
            fetch('/api/games').then(response => response.json())
        ])
        .then(([userData, games]) => {
            // Store userData globally for use in other functions
            window.userData = userData;

            // Process user data
            if (userData.success && userData.user) {
                // Get user notifications
                fetch(`/api/notifications?email=${userData.user.email}`)
                    .then(response => response.json())
                    .then(notifications => {
                        if (Array.isArray(notifications)) {
                            // Update stats
                            const activeCount = notifications.filter(n => !n.triggered).length;
                            const triggeredCount = notifications.filter(n => n.triggered).length;
                            const slotsAvailable = 20 - activeCount; // Assuming Free plan
                            
                            document.getElementById('activeNotifications').textContent = activeCount;
                            document.getElementById('triggeredAlerts').textContent = triggeredCount;
                            document.getElementById('slotsAvailable').textContent = slotsAvailable;
                            
                            // Update recent notifications table
                            const recentNotificationsTable = document.getElementById('recentNotificationsTable');
                            
                            if (recentNotificationsTable && notifications.length > 0) {
                                recentNotificationsTable.innerHTML = '';
                                
                                // Show only the 5 most recent notifications
                                const recentNotifications = notifications.slice(0, 5);
                                
                                recentNotifications.forEach(notification => {
                                    const row = document.createElement('tr');
                                    
                                    // Format date
                                    const createdDate = new Date(notification.created_at);
                                    const formattedDate = createdDate.toLocaleDateString();
                                    
                                    row.innerHTML = `
                                        <td>${notification.card_name}</td>
                                        <td>$${notification.target_price.toFixed(2)}</td>
                                        <td>
                                            ${notification.triggered 
                                                ? '<span class="badge bg-success">Triggered</span>' 
                                                : '<span class="badge bg-primary">Active</span>'}
                                        </td>
                                        <td>${formattedDate}</td>
                                    `;
                                    
                                    recentNotificationsTable.appendChild(row);
                                });
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Error loading notifications:', error);
                    });
            } else {
                // Redirect to login if not authenticated
                window.location.href = '/login';
            }
            
            // Process games data
            if (Array.isArray(games)) {
                // Sort games alphabetically
                games.sort((a, b) => a.localeCompare(b));
                
                gameSelect.innerHTML = '<option value="">Choose a game...</option>';
                games.forEach(game => {
                    const option = document.createElement('option');
                    option.value = game;
                    option.textContent = game;
                    gameSelect.appendChild(option);
                });
            } else {
                gameSelect.innerHTML = '<option value="">Error loading games</option>';
            }
        })
        .catch(error => {
            console.error('Error loading initial data:', error);
            // Handle error state
            gameSelect.innerHTML = '<option value="">Error loading games</option>';
        });
        // Logout functionality
        document.getElementById('logoutBtn').addEventListener('click', function(e) {
            e.preventDefault();
            
            fetch('/api/logout', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.href = '/';
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
        });
        
        // Game dropdown change event
        gameSelect.addEventListener('change', function() {
            const selectedGame = this.value;
            if (selectedGame) {
                loadExpansions(selectedGame);
                resetExpansionAndCard();
            } else {
                resetExpansionAndCard();
            }
        });
        
        // Expansion dropdown change event
        expansionSelect.addEventListener('change', function() {
            const selectedGame = gameSelect.value;
            const selectedExpansion = this.value;
            if (selectedGame && selectedExpansion) {
                loadCardsByExpansion(selectedGame, selectedExpansion);
                loadExpansionTotalValue(selectedGame, selectedExpansion);
            } else {
                hideExpansionValue();
            }
        });
        
        // Search button click event
        cardSearchButton.addEventListener('click', searchCards);
        
        // Search on Enter key
        cardSearchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchCards();
            }
        });
        
        // Toggle sealed products event
        document.getElementById('includeSealedToggle').addEventListener('change', function() {
            // If we have an active game and expansion, reload cards
            const selectedGame = gameSelect.value;
            const selectedExpansion = expansionSelect.value;

            if (selectedGame && selectedExpansion) {
                loadCardsByExpansion(selectedGame, selectedExpansion);
            } else if (cardSearchInput.value.trim().length >= 2) {
                // If we have a search term, re-run the search
                searchCards();
            }
        });

        // Table filter functionality
        tableFilter.addEventListener('input', function() {
            filterTable();
        });

        clearFilter.addEventListener('click', function() {
            tableFilter.value = '';
            filterTable();
            tableFilter.focus();
        });

        // Event delegation for track buttons and expand buttons (prevents multiple listeners)
        // Use a flag to prevent multiple event listeners
        if (!window.tcgToolsEventListenerAdded) {
            window.tcgToolsEventListenerAdded = true;

            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('track-btn')) {
                    const cardId = e.target.getAttribute('data-card-id');
                    const cardName = e.target.getAttribute('data-card-name');
                    const productId = e.target.getAttribute('data-product-id');

                    // Navigate to the set alert page with return URL
                    const currentUrl = encodeURIComponent(window.location.href);
                    const alertUrl = `/set-alert?cardId=${encodeURIComponent(cardId)}&cardName=${encodeURIComponent(cardName)}&returnUrl=${currentUrl}`;
                    window.location.href = alertUrl;
                } else if (e.target.classList.contains('expand-btn') || e.target.closest('.expand-btn')) {
                    // Handle expand button clicks (including clicks on the icon inside)
                    const expandBtn = e.target.classList.contains('expand-btn') ? e.target : e.target.closest('.expand-btn');
                    handleExpandButtonClick(expandBtn);
                }
            });
        }

        // Setup sorting functionality (only if not already set up)
        if (!window.tcgToolsSortingSetup) {
            window.tcgToolsSortingSetup = true;

            document.querySelectorAll('.sortable').forEach(header => {
                header.addEventListener('click', function() {
                const sortType = this.getAttribute('data-sort');
                const isAscending = !this.classList.contains('sort-asc');
                
                // Reset all headers
                document.querySelectorAll('.sortable').forEach(h => {
                    h.classList.remove('sort-asc', 'sort-desc');
                });
                
                // Set current header sort direction
                this.classList.add(isAscending ? 'sort-asc' : 'sort-desc');
                
                // Get all rows
                const rows = Array.from(searchResultsBody.querySelectorAll('tr'));
                
                // Skip if we only have placeholder rows or loading indicators
                if (rows.length <= 1 || rows.some(row => row.querySelector('td[colspan]'))) {
                    return;
                }
                
                // Filter out any rows that don't have the expected structure
                const validRows = rows.filter(row => row.cells.length >= 6 && !row.classList.contains('detail-row'));
                
                if (validRows.length === 0) {
                    return;
                }
                
                // Sort the valid rows
                sortTableRows(validRows, sortType, isAscending);
                
                // Re-append valid rows in new order
                validRows.forEach(row => searchResultsBody.appendChild(row));
            });
        });
        }

        // Function to sort table rows
        function sortTableRows(rows, sortType, isAscending) {
            rows.sort((rowA, rowB) => {
                let valueA, valueB;
                
                switch(sortType) {
                    case 'name':
                        valueA = rowA.cells[2].textContent.trim();
                        valueB = rowB.cells[2].textContent.trim();
                        return isAscending ? 
                            valueA.localeCompare(valueB) : 
                            valueB.localeCompare(valueA);
                    
                    case 'expansion':
                        valueA = rowA.cells[3].textContent.trim();
                        valueB = rowB.cells[3].textContent.trim();
                        return isAscending ? 
                            valueA.localeCompare(valueB) : 
                            valueB.localeCompare(valueA);
                    
                    case 'number':
                        // Extract numeric part from card number
                        valueA = rowA.cells[4].textContent.trim();
                        valueB = rowB.cells[4].textContent.trim();
                        
                        // Try to extract numeric values for proper numeric sorting
                        const numA = parseInt(valueA.replace(/\D/g, '')) || 0;
                        const numB = parseInt(valueB.replace(/\D/g, '')) || 0;
                        
                        return isAscending ? numA - numB : numB - numA;
                    
                    case 'price':
                        // Extract price values from the price column (index 5)
                        valueA = rowA.cells[5].textContent.trim();
                        valueB = rowB.cells[5].textContent.trim();
                        
                        // Convert to numeric values for sorting
                        const priceA = parseFloat(valueA.replace(/[^0-9.-]+/g, '')) || 0;
                        const priceB = parseFloat(valueB.replace(/[^0-9.-]+/g, '')) || 0;
                        
                        // For prices, we might want to handle '-' differently
                        if (valueA === '-' && valueB !== '-') return isAscending ? -1 : 1;
                        if (valueA !== '-' && valueB === '-') return isAscending ? 1 : -1;
                        if (valueA === '-' && valueB === '-') return 0;
                        
                        return isAscending ? priceA - priceB : priceB - priceA;
                    
                    default:
                        return 0;
                }
            });
        }
        
        
        // Function to load expansions
        function loadExpansions(game) {
            expansionSelect.innerHTML = '<option value="">Loading expansions...</option>';
            expansionSelect.disabled = true;
            
            fetch(`/api/expansions/${encodeURIComponent(game)}`)
                .then(response => response.json())
                .then(expansions => {
                    // Sort expansions alphabetically
                    expansions.sort((a, b) => a.localeCompare(b));
                    
                    expansionSelect.innerHTML = '<option value="">Choose an expansion...</option>';
                    expansions.forEach(expansion => {
                        const option = document.createElement('option');
                        option.value = expansion;
                        option.textContent = expansion;
                        expansionSelect.appendChild(option);
                    });
                    expansionSelect.disabled = false;
                })
                .catch(error => {
                    console.error('Error loading expansions:', error);
                    expansionSelect.innerHTML = '<option value="">Error loading expansions</option>';
                    expansionSelect.disabled = false;
                });
        }
        
        // Function to reset expansion and card dropdowns
        function resetExpansionAndCard() {
            expansionSelect.innerHTML = '<option value="">Choose an expansion...</option>';
            expansionSelect.disabled = true;

            // Hide expansion value
            hideExpansionValue();

            // Hide filter box
            tableFilterContainer.style.display = 'none';

            // Hide results table and show placeholder
            document.getElementById('searchResults').style.display = 'none';
            document.getElementById('searchPlaceholder').style.display = 'block';

            // Clear search results
            searchResultsBody.innerHTML = '';
        }

        // Function to load expansion total value
        function loadExpansionTotalValue(game, expansion) {
            const expansionValueContainer = document.getElementById('expansionValueContainer');
            const expansionTotalValue = document.getElementById('expansionTotalValue');
            const expansionValueDetails = document.getElementById('expansionValueDetails');
            const variantBreakdown = document.getElementById('variantBreakdown');
            const variantBreakdownContent = document.getElementById('variantBreakdownContent');

            // Show container and set loading state
            expansionValueContainer.style.display = 'block';
            expansionTotalValue.textContent = 'Loading...';
            expansionValueDetails.textContent = 'Fetching expansion value...';
            variantBreakdown.style.display = 'none';
            expansionValueMeta.style.display = 'none';

            // Set loading badge
            valueSourceBadge.textContent = 'Loading...';
            valueSourceBadge.className = 'badge bg-secondary';

            const startTime = performance.now();

            fetch(`/api/expansion-total-value/${encodeURIComponent(game)}/${encodeURIComponent(expansion)}`)
                .then(response => response.json())
                .then(data => {
                    const endTime = performance.now();
                    const responseTime = Math.round(endTime - startTime);

                    if (data.success) {
                        // Update main value display
                        expansionTotalValue.textContent = `$${data.totalValue.toFixed(2)}`;
                        expansionValueDetails.textContent =
                            `${data.totalVariants} total variants across ${data.cardsWithPrices} cards with prices (${data.cardsWithoutPrices} cards without prices, ${data.totalCards} total cards)`;

                        // Update source badge and metadata
                        const isCached = data.cached === true;
                        if (isCached) {
                            valueSourceBadge.textContent = `Cached (${responseTime}ms)`;
                            valueSourceBadge.className = 'badge bg-success';
                        } else {
                            valueSourceBadge.textContent = `Real-time (${responseTime}ms)`;
                            valueSourceBadge.className = 'badge bg-warning text-dark';
                        }

                        // Show calculation metadata
                        if (data.calculatedAt) {
                            const calcDate = new Date(data.calculatedAt);
                            calculatedAt.textContent = `Calculated: ${calcDate.toLocaleDateString()} ${calcDate.toLocaleTimeString()}`;
                        } else {
                            calculatedAt.textContent = `Calculated: Just now`;
                        }

                        // Set up history link
                        viewHistoryLink.onclick = (e) => {
                            e.preventDefault();
                            showExpansionHistory(game, expansion);
                        };

                        expansionValueMeta.style.display = 'block';

                        // Display variant breakdown if available
                        if (data.variantBreakdown && Object.keys(data.variantBreakdown).length > 0) {
                            variantBreakdownContent.innerHTML = '';

                            // Sort variants by total value (highest first)
                            const sortedVariants = Object.entries(data.variantBreakdown)
                                .sort(([,a], [,b]) => b.total_value - a.total_value);

                            sortedVariants.forEach(([variantName, variantData]) => {
                                const variantDiv = document.createElement('div');
                                variantDiv.className = 'mb-2';

                                const badge = document.createElement('span');
                                badge.className = 'badge bg-light text-dark me-2';
                                badge.innerHTML = `${variantName}: ${variantData.count} cards, $${variantData.total_value.toFixed(2)}`;

                                const highestCard = document.createElement('small');
                                highestCard.className = 'text-muted ms-1';
                                if (variantData.highest_card && variantData.highest_card.name) {
                                    highestCard.innerHTML = `(Highest: ${variantData.highest_card.name} - $${variantData.highest_card.price.toFixed(2)})`;
                                }

                                variantDiv.appendChild(badge);
                                if (variantData.highest_card && variantData.highest_card.name) {
                                    variantDiv.appendChild(highestCard);
                                }
                                variantBreakdownContent.appendChild(variantDiv);
                            });

                            variantBreakdown.style.display = 'block';
                        }
                    } else {
                        expansionTotalValue.textContent = 'Error';
                        expansionValueDetails.textContent = 'Failed to calculate expansion value';
                        valueSourceBadge.textContent = 'Error';
                        valueSourceBadge.className = 'badge bg-danger';
                        variantBreakdown.style.display = 'none';
                        expansionValueMeta.style.display = 'none';
                    }
                })
                .catch(error => {
                    console.error('Error loading expansion total value:', error);
                    expansionTotalValue.textContent = 'Error';
                    expansionValueDetails.textContent = 'Failed to load expansion value';
                    valueSourceBadge.textContent = 'Error';
                    valueSourceBadge.className = 'badge bg-danger';
                    variantBreakdown.style.display = 'none';
                    expansionValueMeta.style.display = 'none';
                });
        }

        // Function to hide expansion value
        function hideExpansionValue() {
            const expansionValueContainer = document.getElementById('expansionValueContainer');
            expansionValueContainer.style.display = 'none';
        }

        // Function to handle expand button clicks
        function handleExpandButtonClick(expandBtn) {
            const rowId = expandBtn.getAttribute('data-row-id');
            const detailRow = document.getElementById(rowId);
            const icon = expandBtn.querySelector('i');
            const productId = expandBtn.closest('tr').querySelector('.track-btn').getAttribute('data-product-id');

            if (detailRow.style.display === 'none') {
                // Expand
                detailRow.style.display = 'table-row';
                icon.classList.remove('fa-chevron-down');
                icon.classList.add('fa-chevron-up');

                // Only fetch listings if we have a product ID and haven't loaded them yet
                if (productId && !detailRow.hasAttribute('data-loaded')) {
                    // Fetch lowest listings for this card using the card ID, not product ID
                    const cardId = expandBtn.closest('tr').querySelector('.track-btn').getAttribute('data-card-id');
                    fetch(`/api/card/${cardId}`)
                        .then(response => response.json())
                        .then(cardData => {
                            if (cardData && cardData.lowestListings && cardData.lowestListings.length > 0) {
                                // Group listings by printing type
                                const listingsByPrinting = {};
                                cardData.lowestListings.forEach(listing => {
                                    const printing = listing.printing || 'Normal';
                                    if (!listingsByPrinting[printing]) {
                                        listingsByPrinting[printing] = [];
                                    }
                                    listingsByPrinting[printing].push(listing);
                                });

                                // Get all available printing types
                                const printingTypes = Object.keys(listingsByPrinting);

                                // Create content with tabs for each printing type
                                let listingsContent = `
                                    <td colspan="8" class="p-3 bg-light">
                                        <div class="printing-tabs mb-3">
                                            <div class="btn-group" role="group" aria-label="Printing types">
                                                ${printingTypes.map((printing, index) => `
                                                    <button type="button" class="btn ${index === 0 ? 'btn-primary' : 'btn-outline-primary'}"
                                                            onclick="showPrintingTab('${printing}', '${rowId}')"
                                                            id="tab-${rowId}-${printing.replace(/\s+/g, '-').toLowerCase()}">
                                                        ${printing}
                                                    </button>
                                                `).join('')}
                                            </div>
                                        </div>

                                        ${printingTypes.map((printing, index) => {
                                            // Sort listings by price only (not including shipping)
                                            listingsByPrinting[printing].sort((a, b) => a.price - b.price);

                                            // Take up to 10 lowest listings for each variant
                                            const topListings = listingsByPrinting[printing].slice(0, 10);

                                            // Determine trend price for this printing variant
                                            let trendPrice = 0;
                                            if (cardData.priceVariants && cardData.priceVariants.length > 0) {
                                                const matchingVariant = cardData.priceVariants.find(v => v.variant === printing) || cardData.priceVariants[0];
                                                trendPrice = matchingVariant.lowPrice;
                                            }

                                            return `
                                                <div class="listings-table-container printing-tab ${index === 0 ? '' : 'd-none'}"
                                                     id="listings-${rowId}-${printing.replace(/\s+/g, '-').toLowerCase()}">
                                                    <table class="table table-hover table-sm">
                                                        <thead class="table-secondary">
                                                            <tr>
                                                                <th>Seller</th>
                                                                <th>Price</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            ${topListings.map(listing => {
                                                                const totalPrice = listing.price + listing.shipping;
                                                                // Determine if price is below/matching trend (green) or above trend (red)
                                                                const priceClass = totalPrice <= trendPrice * 1.05 ? 'text-success' : 'text-danger';

                                                                return `
                                                                    <tr class="${priceClass}">
                                                                        <td>${listing.seller}</td>
                                                                        <td>$${listing.price.toFixed(2)}</td>
                                                                    </tr>
                                                                `;
                                                            }).join('')}
                                                        </tbody>
                                                    </table>
                                                </div>
                                            `;
                                        }).join('')}
                                    </td>
                                `;

                                // Update the detail row with the listings content
                                detailRow.innerHTML = listingsContent;
                                detailRow.setAttribute('data-loaded', 'true');
                            } else {
                                // No listings found
                                detailRow.innerHTML = `
                                    <td colspan="8" class="p-3 bg-light">
                                        <div class="alert alert-info mb-0">
                                            No listing data available for this card.
                                        </div>
                                    </td>
                                `;
                                detailRow.setAttribute('data-loaded', 'true');
                            }
                        })
                        .catch(error => {
                            console.error('Error fetching listings:', error);
                            detailRow.innerHTML = `
                                <td colspan="8" class="p-3 bg-light">
                                    <div class="alert alert-danger mb-0">
                                        Error loading listings data. Please try again.
                                    </div>
                                </td>
                            `;
                        });
                }
            } else {
                // Collapse
                detailRow.style.display = 'none';
                icon.classList.remove('fa-chevron-up');
                icon.classList.add('fa-chevron-down');
            }
        }

        // Function to show expansion history modal
        function showExpansionHistory(game, expansion) {
            const modal = new bootstrap.Modal(document.getElementById('expansionHistoryModal'));
            const historyLoadingSpinner = document.getElementById('historyLoadingSpinner');
            const historyContent = document.getElementById('historyContent');
            const historyError = document.getElementById('historyError');
            const historyExpansionName = document.getElementById('historyExpansionName');
            const historyTableBody = document.getElementById('historyTableBody');

            // Set modal title and show loading state
            historyExpansionName.textContent = `${game} - ${expansion}`;
            historyLoadingSpinner.style.display = 'block';
            historyContent.style.display = 'none';
            historyError.style.display = 'none';

            modal.show();

            // Fetch historical data
            fetch(`/api/expansion-value-history/${encodeURIComponent(game)}/${encodeURIComponent(expansion)}?days=30`)
                .then(response => response.json())
                .then(data => {
                    historyLoadingSpinner.style.display = 'none';

                    if (data.success && data.history && data.history.length > 0) {
                        // Show content and populate table
                        historyContent.style.display = 'block';
                        historyTableBody.innerHTML = '';

                        data.history.forEach((record, index) => {
                            const row = document.createElement('tr');

                            // Calculate change from previous day
                            let changeHtml = '<span class="text-muted">-</span>';
                            if (index < data.history.length - 1) {
                                const currentValue = record.totalValue;
                                const previousValue = data.history[index + 1].totalValue;
                                const change = currentValue - previousValue;
                                const changePercent = ((change / previousValue) * 100).toFixed(1);

                                if (change > 0) {
                                    changeHtml = `<span class="text-success">+$${change.toFixed(2)} (+${changePercent}%)</span>`;
                                } else if (change < 0) {
                                    changeHtml = `<span class="text-danger">$${change.toFixed(2)} (${changePercent}%)</span>`;
                                } else {
                                    changeHtml = '<span class="text-muted">No change</span>';
                                }
                            }

                            row.innerHTML = `
                                <td>${record.date}</td>
                                <td class="fw-bold">$${record.totalValue.toFixed(2)}</td>
                                <td>${record.totalCards}</td>
                                <td>${record.totalVariants}</td>
                                <td>${changeHtml}</td>
                            `;

                            historyTableBody.appendChild(row);
                        });
                    } else {
                        // Show error message
                        historyError.style.display = 'block';
                        document.getElementById('historyErrorMessage').textContent =
                            data.history && data.history.length === 0
                                ? 'No historical data available for this expansion yet. Data will be available after the daily calculation runs.'
                                : 'Failed to load historical data.';
                    }
                })
                .catch(error => {
                    console.error('Error loading expansion history:', error);
                    historyLoadingSpinner.style.display = 'none';
                    historyError.style.display = 'block';
                    document.getElementById('historyErrorMessage').textContent = 'Failed to load historical data.';
                });
        }

        // Price alert functionality moved to dedicated /set-alert page

        // Function to filter table rows
        function filterTable() {
            const filterValue = tableFilter.value.toLowerCase().trim();
            const rows = searchResultsBody.querySelectorAll('tr');
            let visibleCount = 0;
            let totalCount = 0;

            rows.forEach(row => {
                // Skip placeholder rows and detail rows
                if (row.querySelector('td[colspan]') || row.classList.contains('detail-row')) {
                    return;
                }

                totalCount++;

                if (filterValue === '') {
                    // Show all rows when filter is empty
                    row.style.display = '';
                    visibleCount++;
                } else {
                    // Get card name from the third column (index 2)
                    const cardNameCell = row.cells[2];
                    if (cardNameCell) {
                        const cardName = cardNameCell.textContent.toLowerCase();
                        if (cardName.includes(filterValue)) {
                            row.style.display = '';
                            visibleCount++;
                        } else {
                            row.style.display = 'none';
                            // Also hide any associated detail row
                            const expandBtn = row.querySelector('.expand-btn');
                            if (expandBtn) {
                                const rowId = expandBtn.getAttribute('data-row-id');
                                const detailRow = document.getElementById(rowId);
                                if (detailRow) {
                                    detailRow.style.display = 'none';
                                    // Reset expand button
                                    const icon = expandBtn.querySelector('i');
                                    icon.classList.remove('fa-chevron-up');
                                    icon.classList.add('fa-chevron-down');
                                }
                            }
                        }
                    }
                }
            });

            // Update results count
            if (totalCount === 0) {
                filterResultsCount.textContent = 'No results to filter';
            } else if (filterValue === '') {
                filterResultsCount.textContent = `Showing all ${totalCount} results`;
            } else {
                filterResultsCount.textContent = `Showing ${visibleCount} of ${totalCount} results`;
            }
        }
        
        // Function to load cards by expansion
        function loadCardsByExpansion(game, expansion) {
            // Show results table and hide placeholder
            document.getElementById('searchResults').style.display = 'block';
            document.getElementById('searchPlaceholder').style.display = 'none';

            // Show loading state
            searchResultsBody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center py-4">
                        <div class="spinner-border spinner-border-sm text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <small class="ms-2">Loading cards...</small>
                    </td>
                </tr>
            `;
            
            const includeSealed = document.getElementById('includeSealedToggle').checked;
            
            // Add includeSealed as a query parameter
            const url = `/api/cards-by-expansion/${encodeURIComponent(game)}/${encodeURIComponent(expansion)}?includeSealed=${includeSealed}`;
            
            fetch(url)
                .then(response => response.json())
                .then(cards => {
                    // Filter cards based on isSealed property if needed
                    // This is a fallback in case the API doesn't support the includeSealed parameter
                    if (!includeSealed) {
                        cards = cards.filter(card => !card.isSealed);
                    }
                    
                    displayCardsInTable(cards);
                })
                .catch(error => {
                    console.error('Error loading cards:', error);
                    searchResultsBody.innerHTML = `
                        <tr>
                            <td colspan="8" class="text-center text-danger py-4">
                                <i class="fas fa-exclamation-circle fa-2x mb-2"></i>
                                <p class="mb-0">Error loading cards. Please try again.</p>
                            </td>
                        </tr>
                    `;
                });
        }
        
        // Function to search cards
        function searchCards() {
            const searchTerm = cardSearchInput.value.trim();
            const includeSealed = document.getElementById('includeSealedToggle').checked;

            if (searchTerm.length < 2) {
                // Hide results table and show placeholder for invalid search
                document.getElementById('searchResults').style.display = 'none';
                document.getElementById('searchPlaceholder').style.display = 'block';
                return;
            }

            // Show results table and hide placeholder
            document.getElementById('searchResults').style.display = 'block';
            document.getElementById('searchPlaceholder').style.display = 'none';

            // Show loading state
            searchResultsBody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center py-4">
                        <div class="spinner-border spinner-border-sm text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <small class="ms-2">Searching...</small>
                    </td>
                </tr>
            `;
            
            // Fetch cards from catalog
            fetch('/api/cards/search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    pattern: searchTerm,
                    limit: 1000, // Increased limit to get all results
                    includeSealed: includeSealed
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.cards && data.cards.length > 0) {
                    displayCardsInTable(data.cards);
                } else {
                    // No results
                    searchResultsBody.innerHTML = `
                        <tr>
                            <td colspan="8" class="text-center text-muted py-4">
                                <i class="fas fa-search-minus fa-2x mb-2 opacity-50"></i>
                                <p class="mb-0">No cards found matching "${searchTerm}"</p>
                            </td>
                        </tr>
                    `;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                searchResultsBody.innerHTML = `
                    <tr>
                        <td colspan="8" class="text-center text-danger py-4">
                            <i class="fas fa-exclamation-circle fa-2x mb-2"></i>
                            <p class="mb-0">Error searching cards. Please try again.</p>
                        </td>
                    </tr>
                `;
            });
        }
        
        // Function to display cards in table
        function displayCardsInTable(cards) {
            if (!cards || cards.length === 0) {
                searchResultsBody.innerHTML = `
                    <tr>
                        <td colspan="8" class="text-center text-muted py-3">
                            <small>No cards found</small>
                        </td>
                    </tr>
                `;
                return;
            }
            
            searchResultsBody.innerHTML = '';
            
            // Show loading indicator while fetching price data
            const loadingRow = document.createElement('tr');
            loadingRow.id = 'priceLoadingIndicator';
            loadingRow.innerHTML = `
                <td colspan="8" class="text-center py-3">
                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                        <span class="visually-hidden">Loading price data...</span>
                    </div>
                    <small class="ms-2">Loading price data...</small>
                </td>
            `;
            searchResultsBody.appendChild(loadingRow);
            
            // Filter cards that need price data
            const cardsNeedingPrices = cards.filter(card => !card.priceVariants && card.productId);
            
            // If no cards need price data, just display them
            if (cardsNeedingPrices.length === 0) {
                displayCardsWithPrices(cards);
                return;
            }
            
            // Group cards into batches of 10 to avoid overwhelming the server
            const batchSize = 10;
            const batches = [];
            
            for (let i = 0; i < cardsNeedingPrices.length; i += batchSize) {
                batches.push(cardsNeedingPrices.slice(i, i + batchSize));
            }
            
            // Process each batch sequentially to avoid too many concurrent requests
            const processBatches = async () => {
                const cardPriceMap = {};
                
                for (const batch of batches) {
                    const cardIds = batch.map(card => card._id);
                    
                    try {
                        const response = await fetch('/api/cards/batch-prices', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({ cardIds })
                        });
                        
                        if (response.ok) {
                            const batchPriceData = await response.json();
                            // Merge the batch results into our map
                            Object.assign(cardPriceMap, batchPriceData);
                        }
                    } catch (error) {
                        console.error('Error fetching batch price data:', error);
                    }
                }
                
                // Apply price data to all cards
                cards.forEach(card => {
                    if (cardPriceMap[card._id]) {
                        card.priceVariants = cardPriceMap[card._id];
                    } else if (!card.priceVariants) {
                        card.priceVariants = [];
                    }
                });
                
                // Display cards with price data
                displayCardsWithPrices(cards);
            };
            
            // Start processing batches
            processBatches().catch(error => {
                console.error('Error in batch processing:', error);
                // Fallback to displaying cards without prices
                cards.forEach(card => {
                    if (!card.priceVariants) card.priceVariants = [];
                });
                displayCardsWithPrices(cards);
            });
        }
        
        // Function to display cards with price data
        function displayCardsWithPrices(cardsWithPrices) {
            // Remove loading indicator if it exists
            const loadingIndicator = document.getElementById('priceLoadingIndicator');
            if (loadingIndicator) {
                loadingIndicator.remove();
            }

            // Show filter box if we have cards to display
            if (cardsWithPrices && cardsWithPrices.length > 0) {
                tableFilterContainer.style.display = 'block';
                // Reset filter
                tableFilter.value = '';
                filterResultsCount.textContent = `Showing all ${cardsWithPrices.length} results`;
            } else {
                tableFilterContainer.style.display = 'none';
            }
            
            cardsWithPrices.forEach(card => {
                    const row = document.createElement('tr');
                    
                    // Handle thumbnail
                    const thumbnailUrl = card.imageUrl || card.image || 'https://via.placeholder.com/50?text=No+Image';
                    
                    // Handle set name field which might be named differently
                    const setName = card.set_name || card.setName || card.expansionName || card.set || 'Unknown Set';
                    
                    // Handle card number
                    const cardNumber = card.number || card.cardNumber || '-';
                    
                    // Use _id if id is not available
                    const cardId = card.id || card._id;
                    
                    // Get price variants
                    const priceVariants = card.priceVariants || [];
                    
                    // Find normal, foil, and extended variants
                    const normalVariant = priceVariants.find(v => v.variant === 'Normal' || v.variant === 'Regular');
                    const foilVariant = priceVariants.find(v => v.variant === 'Foil');
                    const extendedVariant = priceVariants.find(v => v.variant === 'Extended' || v.variant === 'Extended Art');
                    
                    // Get the lowest price from all variants for the main price column
                    let lowestPrice = '-';
                    if (priceVariants.length > 0) {
                        const prices = priceVariants
                            .filter(v => v.lowPrice !== undefined && v.lowPrice !== null)
                            .map(v => v.lowPrice);
                        
                        if (prices.length > 0) {
                            const minPrice = Math.min(...prices);
                            lowestPrice = `$${minPrice.toFixed(2)}`;
                        }
                    }
                    
                    // Create variant badges for the variants column
                    let variantBadges = '';
                    if (priceVariants.length > 0) {
                        const validVariants = priceVariants.filter(v => v.variant && v.lowPrice !== undefined && v.lowPrice !== null);
                        if (validVariants.length > 0) {
                            variantBadges = `<span class="variants-badge">${validVariants.length} variant${validVariants.length > 1 ? 's' : ''}</span>`;
                        } else {
                            variantBadges = '<span class="text-muted small">No pricing</span>';
                        }
                    } else {
                        variantBadges = '<span class="text-muted small">No variants</span>';
                    }
                    
                    // Create a unique ID for this card row
                    const rowId = `card-${cardId.replace(/[^a-zA-Z0-9]/g, '')}`;
                    
                    // Add expand/collapse button
                    row.innerHTML = `
                        <td class="text-center">
                            <button class="expand-btn" data-row-id="${rowId}">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </td>
                        <td>
                            <img src="${thumbnailUrl}" alt="${card.name}" class="card-thumbnail">
                        </td>
                        <td>
                            <div class="card-name">${card.name || 'Unknown Card'}</div>
                        </td>
                        <td>
                            <div class="set-name">${setName}</div>
                        </td>
                        <td>
                            <span class="card-number">${cardNumber}</span>
                        </td>
                        <td>
                            <div class="${lowestPrice === '-' ? 'price-unavailable' : 'price-display'}">${lowestPrice}</div>
                        </td>
                        <td>
                            <div class="variants-display">${variantBadges}</div>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <button class="track-btn"
                                    data-card-id="${cardId}"
                                    data-card-name="${card.name || 'Unknown Card'}"
                                    data-product-id="${card.productId || ''}">
                                    <i class="fas fa-bell me-1"></i>Set Alert
                                </button>
                            </div>
                        </td>
                    `;
                    
                    // Create the detail row for expanded view
                    const detailRow = document.createElement('tr');
                    detailRow.id = rowId;
                    detailRow.classList.add('detail-row');
                    detailRow.style.display = 'none'; // Hidden by default
                    
                    // Initially show loading indicator in the detail row
                    detailRow.innerHTML = `
                        <td colspan="8" class="p-3 bg-light text-center">
                            <div class="spinner-border spinner-border-sm text-primary" role="status">
                                <span class="visually-hidden">Loading listings...</span>
                            </div>
                            <small class="ms-2">Loading lowest listings...</small>
                        </td>
                    `;
                    
                    // Add both rows to the table
                    searchResultsBody.appendChild(row);
                    searchResultsBody.appendChild(detailRow);
                });
                
                // Event delegation is handled at the document level - no need to add listeners here
        }
        
        // Function to switch between printing tabs in expanded rows
        window.showPrintingTab = function(printing, rowId) {
            // Normalize the printing name for use in IDs
            const normalizedPrinting = printing.replace(/\s+/g, '-').toLowerCase();
            
            // Hide all tabs for this row
            document.querySelectorAll(`#${rowId} .printing-tab`).forEach(tab => {
                tab.classList.add('d-none');
            });
            
            // Show the selected tab
            const selectedTab = document.getElementById(`listings-${rowId}-${normalizedPrinting}`);
            if (selectedTab) {
                selectedTab.classList.remove('d-none');
            }
            
            // Update button styles
            document.querySelectorAll(`#${rowId} .printing-tabs .btn`).forEach(btn => {
                btn.classList.remove('btn-primary');
                btn.classList.add('btn-outline-primary');
            });
            
            // Highlight the selected button
            const selectedBtn = document.getElementById(`tab-${rowId}-${normalizedPrinting}`);
            if (selectedBtn) {
                selectedBtn.classList.remove('btn-outline-primary');
                selectedBtn.classList.add('btn-primary');
            }
        };
    });
</script>
{% endblock %}
