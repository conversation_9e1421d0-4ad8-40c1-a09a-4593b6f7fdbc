#!/usr/bin/env python3
"""
Notification Checker Script

This script checks for active price notifications and sends emails
when the current price of a card drops to or below the target price.

It's designed to be run as a scheduled task (e.g., via cron).
"""

import logging
import sys
from models import notification_model

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("notification_checker.log"),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def main():
    """
    Main function to check notifications and send emails
    """
    logger.info("Starting notification check...")
    
    try:
        # Check notifications and send emails
        notifications_sent = notification_model.check_and_send_notifications()
        
        logger.info(f"Notification check completed. Sent {notifications_sent} notifications.")
        
    except Exception as e:
        logger.error(f"Error in notification checker: {str(e)}")
        return 1
        
    return 0

if __name__ == "__main__":
    sys.exit(main())
