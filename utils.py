import requests
from config import Config
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def send_email(recipient, subject, html_content, text_content=None):
    """
    Send an email using Mailgun API
    
    Args:
        recipient (str): Email address of the recipient
        subject (str): Subject of the email
        html_content (str): HTML content of the email
        text_content (str, optional): Plain text content of the email. If None, will use html_content with tags stripped.
    
    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    try:
        if text_content is None:
            # Simple HTML tag stripping for plain text alternative
            text_content = html_content.replace('<br>', '\n').replace('<p>', '\n').replace('</p>', '\n')
            text_content = ''.join([i if ord(i) < 128 else ' ' for i in text_content])
            
            # Remove HTML tags
            import re
            text_content = re.sub(r'<[^>]*>', '', text_content)
        
        url = f"{Config.MAILGUN_BASE_URL}/{Config.MAILGUN_DOMAIN}/messages"
        
        data = {
            "from": Config.MAIL_DEFAULT_SENDER,
            "to": recipient,
            "subject": subject,
            "html": html_content,
            "text": text_content
        }
        
        response = requests.post(
            url,
            auth=("api", Config.MAILGUN_API_KEY),
            data=data
        )
        
        if response.status_code == 200:
            logger.info(f"Email sent successfully to {recipient}")
            return True
        else:
            logger.error(f"Failed to send email: {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"Error sending email: {str(e)}")
        return False

def send_notification_email(recipient, card_name, price, listing_url):
    """
    Send a price notification email
    
    Args:
        recipient (str): Email address of the recipient
        card_name (str): Name of the card
        price (float): Current price of the card
        listing_url (str): URL to the listing
        
    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    subject = f"Price Alert: {card_name} is now ${price:.2f}"
    
    html_content = f"""
    <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="background-color: #2962ff; padding: 20px; text-align: center; border-radius: 5px 5px 0 0;">
                    <h1 style="color: white; margin: 0;">TCG Alert</h1>
                </div>
                <div style="background-color: #f9f9f9; padding: 20px; border-radius: 0 0 5px 5px; border: 1px solid #eee;">
                    <h2>Price Alert Notification</h2>
                    <p>Good news! <strong>{card_name}</strong> is now available at your target price.</p>
                    <p>Current price: <strong>${price:.2f}</strong></p>
                    <div style="text-align: center; margin: 30px 0;">
                        <a href="{listing_url}" style="background-color: #2962ff; color: white; padding: 12px 25px; text-decoration: none; border-radius: 4px; font-weight: bold;">View Listing</a>
                    </div>
                    <p>Don't wait too long - card prices can change quickly!</p>
                    <p>Happy collecting,<br>The TCG Alert Team</p>
                </div>
                <div style="text-align: center; padding: 20px; color: #666; font-size: 12px;">
                    <p>© 2024 TCG Alert. All rights reserved.</p>
                    <p>If you no longer wish to receive these emails, you can <a href="#" style="color: #666;">unsubscribe</a>.</p>
                </div>
            </div>
        </body>
    </html>
    """
    
    return send_email(recipient, subject, html_content)

def send_welcome_email(recipient, username, verification_token=None):
    """
    Send a welcome email to new users
    
    Args:
        recipient (str): Email address of the recipient
        username (str): Username of the new user
        verification_token (str, optional): Email verification token
        
    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    subject = "Welcome to TCG Alert!"
    
    # Create verification link if token is provided
    verification_link = ""
    verification_section = ""
    
    if verification_token:
        verification_link = f"https://tcgsync.com/verify-email?token={verification_token}"
        verification_section = f"""
        <div style="background-color: #f0f8ff; padding: 15px; margin: 20px 0; border-left: 4px solid #2962ff; border-radius: 4px;">
            <h3 style="margin-top: 0;">Verify Your Email Address</h3>
            <p>Please verify your email address by clicking the button below:</p>
            <div style="text-align: center; margin: 20px 0;">
                <a href="{verification_link}" style="background-color: #28a745; color: white; padding: 12px 25px; text-decoration: none; border-radius: 4px; font-weight: bold;">Verify Email</a>
            </div>
            <p style="font-size: 12px; color: #666;">Or copy and paste this link into your browser: {verification_link}</p>
            <p style="font-size: 12px; color: #666;">This verification link will expire in 24 hours.</p>
        </div>
        """
    
    html_content = f"""
    <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="background-color: #2962ff; padding: 20px; text-align: center; border-radius: 5px 5px 0 0;">
                    <h1 style="color: white; margin: 0;">TCG Alert</h1>
                </div>
                <div style="background-color: #f9f9f9; padding: 20px; border-radius: 0 0 5px 5px; border: 1px solid #eee;">
                    <h2>Welcome to TCG Alert!</h2>
                    <p>Hi {username},</p>
                    <p>Thank you for joining TCG Alert! We're excited to help you track prices and find great deals on your favorite trading cards.</p>
                    
                    {verification_section}
                    
                    <h3>Getting Started:</h3>
                    <ol>
                        <li>Browse our catalog of cards</li>
                        <li>Set up price alerts for cards you're interested in</li>
                        <li>Receive notifications when prices drop to your target</li>
                    </ol>
                    <div style="text-align: center; margin: 30px 0;">
                        <a href="http://tcgsync.com" style="background-color: #2962ff; color: white; padding: 12px 25px; text-decoration: none; border-radius: 4px; font-weight: bold;">Start Tracking Cards</a>
                    </div>
                    <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>
                    <p>Happy collecting,<br>The TCG Alert Team</p>
                </div>
                <div style="text-align: center; padding: 20px; color: #666; font-size: 12px;">
                    <p>© 2024 TCG Alert. All rights reserved.</p>
                    <p>If you no longer wish to receive these emails, you can <a href="#" style="color: #666;">unsubscribe</a>.</p>
                </div>
            </div>
        </body>
    </html>
    """
    
    return send_email(recipient, subject, html_content)

def send_verification_email(recipient, username, verification_token):
    """
    Send an email verification email
    
    Args:
        recipient (str): Email address of the recipient
        username (str): Username of the user
        verification_token (str): Email verification token
        
    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    subject = "Verify Your TCG Alert Email Address"
    
    verification_link = f"https://tcgsync.com/verify-email?token={verification_token}"
    
    html_content = f"""
    <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="background-color: #2962ff; padding: 20px; text-align: center; border-radius: 5px 5px 0 0;">
                    <h1 style="color: white; margin: 0;">TCG Alert</h1>
                </div>
                <div style="background-color: #f9f9f9; padding: 20px; border-radius: 0 0 5px 5px; border: 1px solid #eee;">
                    <h2>Verify Your Email Address</h2>
                    <p>Hi {username},</p>
                    <p>Thank you for creating an account with TCG Alert. To complete your registration and access all features, please verify your email address by clicking the button below:</p>
                    
                    <div style="text-align: center; margin: 30px 0;">
                        <a href="{verification_link}" style="background-color: #28a745; color: white; padding: 12px 25px; text-decoration: none; border-radius: 4px; font-weight: bold;">Verify Email</a>
                    </div>
                    
                    <p>If the button above doesn't work, copy and paste this URL into your browser:</p>
                    <p style="background-color: #eee; padding: 10px; word-break: break-all;">{verification_link}</p>
                    
                    <p>This verification link will expire in 24 hours.</p>
                    
                    <p>If you did not create an account with TCG Alert, please ignore this email.</p>
                    
                    <p>Happy collecting,<br>The TCG Alert Team</p>
                </div>
                <div style="text-align: center; padding: 20px; color: #666; font-size: 12px;">
                    <p>© 2024 TCG Alert. All rights reserved.</p>
                </div>
            </div>
        </body>
    </html>
    """
    
    return send_email(recipient, subject, html_content)

def send_password_reset_email(recipient, reset_token):
    """
    Send a password reset email
    
    Args:
        recipient (str): Email address of the recipient
        reset_token (str): Password reset token
        
    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    subject = "Reset Your TCG Alert Password"
    
    reset_url = f"http://tcgsync.com/reset-password?token={reset_token}"
    
    html_content = f"""
    <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="background-color: #2962ff; padding: 20px; text-align: center; border-radius: 5px 5px 0 0;">
                    <h1 style="color: white; margin: 0;">TCG Alert</h1>
                </div>
                <div style="background-color: #f9f9f9; padding: 20px; border-radius: 0 0 5px 5px; border: 1px solid #eee;">
                    <h2>Password Reset Request</h2>
                    <p>We received a request to reset your password. If you didn't make this request, you can safely ignore this email.</p>
                    <p>To reset your password, click the button below:</p>
                    <div style="text-align: center; margin: 30px 0;">
                        <a href="{reset_url}" style="background-color: #2962ff; color: white; padding: 12px 25px; text-decoration: none; border-radius: 4px; font-weight: bold;">Reset Password</a>
                    </div>
                    <p>This link will expire in 1 hour for security reasons.</p>
                    <p>If the button above doesn't work, copy and paste this URL into your browser:</p>
                    <p style="background-color: #eee; padding: 10px; word-break: break-all;">{reset_url}</p>
                    <p>The TCG Alert Team</p>
                </div>
                <div style="text-align: center; padding: 20px; color: #666; font-size: 12px;">
                    <p>© 2024 TCG Alert. All rights reserved.</p>
                </div>
            </div>
        </body>
    </html>
    """
    
    return send_email(recipient, subject, html_content)
