@echo off
REM Daily Expansion Value Calculation Script
REM This batch file runs the Python script to calculate expansion values
REM Schedule this to run daily via Windows Task Scheduler

echo Starting daily expansion value calculation...
echo Date: %date% %time%

REM Change to the script directory
cd /d "%~dp0"

REM Run the Python script
python calculate_expansion_values.py

REM Check if the script ran successfully
if %errorlevel% equ 0 (
    echo Daily calculation completed successfully at %date% %time%
) else (
    echo Daily calculation failed with error code %errorlevel% at %date% %time%
)

REM Optional: Add a pause for debugging (remove for automated runs)
REM pause
